<?php
require_once '../includes/functions.php';

// Check if user is logged in
requireLogin();

// Set page variables
$pageTitle = 'Add New Testimonial';
$current_page = 'testimonials.php';

$message = '';
$messageType = '';

// Handle form submission
if ($_POST) {
    $client_name = sanitizeInput($_POST['client_name']);
    $client_position = sanitizeInput($_POST['client_position']);
    $client_company = sanitizeInput($_POST['client_company']);
    $testimonial_text = sanitizeInput($_POST['testimonial_text']);
    $rating = (int)$_POST['rating'];
    $status = sanitizeInput($_POST['status']);
    $featured = isset($_POST['featured']) ? 1 : 0;
    $display_order = (int)$_POST['display_order'];
    
    if ($client_name && $testimonial_text && $rating >= 1 && $rating <= 5) {
        // Handle client image upload
        $clientImage = null;
        if (isset($_FILES['client_image']) && $_FILES['client_image']['error'] === UPLOAD_ERR_OK) {
            $uploadResult = uploadFile($_FILES['client_image'], '../uploads/avatars/');
            if ($uploadResult['success']) {
                $clientImage = $uploadResult['relative_path'];
            } else {
                $message = 'Image upload failed: ' . $uploadResult['message'];
                $messageType = 'error';
            }
        }
        
        if (!$message) {
            $testimonialData = [
                'client_name' => $client_name,
                'client_position' => $client_position,
                'client_company' => $client_company,
                'client_image' => $clientImage,
                'testimonial_text' => $testimonial_text,
                'rating' => $rating,
                'status' => $status,
                'featured' => $featured,
                'display_order' => $display_order
            ];
            
            if (addTestimonial($testimonialData)) {
                $message = 'Testimonial added successfully!';
                $messageType = 'success';
                
                // Clear form data
                $_POST = [];
            } else {
                $message = 'Failed to add testimonial. Please try again.';
                $messageType = 'error';
            }
        }
    } else {
        $message = 'Please fill in all required fields and ensure rating is between 1-5.';
        $messageType = 'error';
    }
}

// Get next display order
$existingTestimonials = getTestimonials();
$nextOrder = count($existingTestimonials) + 1;

include 'includes/admin_header.php';
?>

<!-- Add Testimonial Content -->
<div class="page-header d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="mb-1">Add New Testimonial</h2>
        <p class="text-muted mb-0">Create a new client testimonial to showcase on your website.</p>
    </div>
    <div>
        <a href="testimonials.php" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> Back to Testimonials
        </a>
    </div>
</div>

<!-- Display Messages -->
<?php if ($message): ?>
    <div class="alert alert-<?php echo $messageType === 'success' ? 'success' : 'danger'; ?> alert-dismissible fade show" role="alert">
        <i class="fas fa-<?php echo $messageType === 'success' ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
        <?php echo htmlspecialchars($message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<div class="row">
    <div class="col-lg-8">
        <!-- Main Form -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-quote-left me-2"></i>
                    Testimonial Information
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data" id="testimonialForm">
                    <div class="row">
                        <!-- Client Name -->
                        <div class="col-md-6 mb-3">
                            <label for="client_name" class="form-label">
                                Client Name <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="client_name" name="client_name" 
                                   value="<?php echo htmlspecialchars($_POST['client_name'] ?? ''); ?>" 
                                   required maxlength="255">
                            <div class="form-text">Full name of the client providing the testimonial</div>
                        </div>
                        
                        <!-- Client Position -->
                        <div class="col-md-6 mb-3">
                            <label for="client_position" class="form-label">Position/Title</label>
                            <input type="text" class="form-control" id="client_position" name="client_position" 
                                   value="<?php echo htmlspecialchars($_POST['client_position'] ?? ''); ?>" 
                                   maxlength="255">
                            <div class="form-text">Job title or position (optional)</div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <!-- Client Company -->
                        <div class="col-md-6 mb-3">
                            <label for="client_company" class="form-label">Company/Organization</label>
                            <input type="text" class="form-control" id="client_company" name="client_company" 
                                   value="<?php echo htmlspecialchars($_POST['client_company'] ?? ''); ?>" 
                                   maxlength="255">
                            <div class="form-text">Company or organization name (optional)</div>
                        </div>
                        
                        <!-- Rating -->
                        <div class="col-md-6 mb-3">
                            <label for="rating" class="form-label">
                                Rating <span class="text-danger">*</span>
                            </label>
                            <select class="form-select" id="rating" name="rating" required>
                                <option value="">Select Rating</option>
                                <?php for ($i = 5; $i >= 1; $i--): ?>
                                    <option value="<?php echo $i; ?>" <?php echo (($_POST['rating'] ?? 5) == $i) ? 'selected' : ''; ?>>
                                        <?php echo $i; ?> Star<?php echo $i > 1 ? 's' : ''; ?>
                                        <?php echo str_repeat('★', $i) . str_repeat('☆', 5-$i); ?>
                                    </option>
                                <?php endfor; ?>
                            </select>
                        </div>
                    </div>
                    
                    <!-- Client Image -->
                    <div class="mb-3">
                        <label for="client_image" class="form-label">Client Photo</label>
                        <input type="file" class="form-control" id="client_image" name="client_image" 
                               accept="image/*">
                        <div class="form-text">Upload a photo of the client (optional). Recommended size: 200x200px</div>
                        <div id="imagePreview" class="mt-2"></div>
                    </div>
                    
                    <!-- Testimonial Text -->
                    <div class="mb-3">
                        <label for="testimonial_text" class="form-label">
                            Testimonial Text <span class="text-danger">*</span>
                        </label>
                        <textarea class="form-control" id="testimonial_text" name="testimonial_text" 
                                  rows="6" required maxlength="1000"><?php echo htmlspecialchars($_POST['testimonial_text'] ?? ''); ?></textarea>
                        <div class="form-text">
                            The testimonial content (max 1000 characters)
                            <span id="charCount" class="float-end">0/1000</span>
                        </div>
                    </div>
                    
                    <div class="row">
                        <!-- Status -->
                        <div class="col-md-4 mb-3">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-select" id="status" name="status">
                                <option value="active" <?php echo (($_POST['status'] ?? 'active') === 'active') ? 'selected' : ''; ?>>Active</option>
                                <option value="inactive" <?php echo (($_POST['status'] ?? '') === 'inactive') ? 'selected' : ''; ?>>Inactive</option>
                            </select>
                        </div>
                        
                        <!-- Display Order -->
                        <div class="col-md-4 mb-3">
                            <label for="display_order" class="form-label">Display Order</label>
                            <input type="number" class="form-control" id="display_order" name="display_order" 
                                   value="<?php echo $_POST['display_order'] ?? $nextOrder; ?>" 
                                   min="1" max="999">
                            <div class="form-text">Order in which testimonial appears</div>
                        </div>
                        
                        <!-- Featured -->
                        <div class="col-md-4 mb-3">
                            <label class="form-label">Options</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="featured" name="featured" 
                                       value="1" <?php echo isset($_POST['featured']) ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="featured">
                                    <i class="fas fa-star text-warning me-1"></i>
                                    Featured Testimonial
                                </label>
                                <div class="form-text">Show prominently on homepage</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Submit Buttons -->
                    <div class="d-flex gap-2 pt-3 border-top">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Save Testimonial
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="resetForm()">
                            <i class="fas fa-undo"></i> Reset Form
                        </button>
                        <a href="testimonials.php" class="btn btn-outline-danger ms-auto">
                            <i class="fas fa-times"></i> Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- Preview Card -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-eye me-2"></i>
                    Live Preview
                </h6>
            </div>
            <div class="card-body">
                <div id="testimonialPreview" class="testimonial-preview-card">
                    <div class="rating-preview mb-2">
                        <span id="previewRating">
                            ★★★★★
                        </span>
                    </div>
                    <blockquote class="blockquote">
                        <p id="previewText" class="mb-3">"Enter testimonial text to see preview..."</p>
                    </blockquote>
                    <div class="d-flex align-items-center">
                        <div id="previewAvatar" class="avatar-placeholder me-3">
                            ?
                        </div>
                        <div>
                            <div id="previewName" class="fw-semibold">Client Name</div>
                            <small id="previewPosition" class="text-muted">Position at Company</small>
                        </div>
                    </div>
                    <div id="previewFeatured" class="mt-2" style="display: none;">
                        <span class="badge bg-warning">
                            <i class="fas fa-star"></i> Featured
                        </span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Tips Card -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>
                    Tips for Great Testimonials
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        Keep testimonials authentic and specific
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        Include client's full name and position
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        Use high-quality client photos
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        Highlight specific benefits or results
                    </li>
                    <li>
                        <i class="fas fa-check text-success me-2"></i>
                        Feature your best testimonials prominently
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

<style>
.testimonial-preview-card {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
    border-left: 4px solid #007bff;
}

.avatar-placeholder {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 1.2rem;
}

.rating-preview {
    font-size: 1.2rem;
    color: #ffc107;
}

#imagePreview img {
    max-width: 100px;
    max-height: 100px;
    border-radius: 8px;
    border: 2px solid #dee2e6;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Character counter for testimonial text
    const testimonialText = document.getElementById('testimonial_text');
    const charCount = document.getElementById('charCount');

    function updateCharCount() {
        const count = testimonialText.value.length;
        charCount.textContent = count + '/1000';
        charCount.className = count > 900 ? 'float-end text-warning' : 'float-end';
        if (count > 1000) {
            charCount.className = 'float-end text-danger';
        }
    }

    testimonialText.addEventListener('input', updateCharCount);
    updateCharCount();

    // Image preview
    const imageInput = document.getElementById('client_image');
    const imagePreview = document.getElementById('imagePreview');

    imageInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                imagePreview.innerHTML = `
                    <img src="${e.target.result}" alt="Preview" class="img-thumbnail">
                    <div class="mt-1 text-muted small">${file.name}</div>
                `;
                updatePreview();
            };
            reader.readAsDataURL(file);
        } else {
            imagePreview.innerHTML = '';
            updatePreview();
        }
    });

    // Live preview updates
    function updatePreview() {
        const name = document.getElementById('client_name').value || 'Client Name';
        const position = document.getElementById('client_position').value;
        const company = document.getElementById('client_company').value;
        const text = document.getElementById('testimonial_text').value || 'Enter testimonial text to see preview...';
        const rating = parseInt(document.getElementById('rating').value) || 5;
        const featured = document.getElementById('featured').checked;

        // Update preview elements
        document.getElementById('previewName').textContent = name;

        let positionText = '';
        if (position && company) {
            positionText = position + ' at ' + company;
        } else if (position) {
            positionText = position;
        } else if (company) {
            positionText = company;
        } else {
            positionText = 'Position at Company';
        }
        document.getElementById('previewPosition').textContent = positionText;

        document.getElementById('previewText').textContent = '"' + text + '"';

        // Update rating stars
        const ratingStars = '★'.repeat(rating) + '☆'.repeat(5 - rating);
        document.getElementById('previewRating').textContent = ratingStars;

        // Update avatar
        const avatar = document.getElementById('previewAvatar');
        const imagePreview = document.querySelector('#imagePreview img');
        if (imagePreview) {
            avatar.innerHTML = `<img src="${imagePreview.src}" alt="${name}" style="width: 50px; height: 50px; border-radius: 50%; object-fit: cover;">`;
        } else {
            avatar.textContent = name.charAt(0).toUpperCase();
        }

        // Update featured badge
        const featuredBadge = document.getElementById('previewFeatured');
        featuredBadge.style.display = featured ? 'block' : 'none';
    }

    // Add event listeners for live preview
    document.getElementById('client_name').addEventListener('input', updatePreview);
    document.getElementById('client_position').addEventListener('input', updatePreview);
    document.getElementById('client_company').addEventListener('input', updatePreview);
    document.getElementById('testimonial_text').addEventListener('input', updatePreview);
    document.getElementById('rating').addEventListener('change', updatePreview);
    document.getElementById('featured').addEventListener('change', updatePreview);

    // Initial preview update
    updatePreview();
});

function resetForm() {
    if (confirm('Are you sure you want to reset the form? All entered data will be lost.')) {
        document.getElementById('testimonialForm').reset();
        document.getElementById('imagePreview').innerHTML = '';
        document.getElementById('charCount').textContent = '0/1000';
        document.getElementById('charCount').className = 'float-end';

        // Reset preview
        setTimeout(function() {
            document.querySelector('script').dispatchEvent(new Event('DOMContentLoaded'));
        }, 100);
    }
}

// Form validation
document.getElementById('testimonialForm').addEventListener('submit', function(e) {
    const name = document.getElementById('client_name').value.trim();
    const text = document.getElementById('testimonial_text').value.trim();
    const rating = document.getElementById('rating').value;

    if (!name || !text || !rating) {
        e.preventDefault();
        alert('Please fill in all required fields (Client Name, Testimonial Text, and Rating).');
        return false;
    }

    if (text.length > 1000) {
        e.preventDefault();
        alert('Testimonial text must be 1000 characters or less.');
        return false;
    }

    return true;
});
</script>

<?php include 'includes/admin_footer.php'; ?>
