<?php
require_once 'includes/functions.php';

$pageTitle = 'Header Test';
$pageDescription = 'Testing the updated header functions and navigation for Arcke Interior Design.';
$isHomePage = false;

include 'includes/header.php';
?>

<main id="main" role="main">
    <!-- Test Section -->
    <section class="test-section section-padding">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <h1>Header Functions Test Page</h1>
                    <p>This page tests the updated header functions and navigation system.</p>
                    
                    <h2>Navigation Test</h2>
                    <p>The navigation should be consistent with the pattern used in includes/header.php.</p>
                    
                    <h3>Features Tested:</h3>
                    <ul>
                        <li>✅ Dynamic navigation generation</li>
                        <li>✅ Active page detection</li>
                        <li>✅ Social media links management</li>
                        <li>✅ Responsive mobile navigation</li>
                        <li>✅ Accessibility features</li>
                        <li>✅ SEO meta tags</li>
                        <li>✅ Structured data</li>
                    </ul>
                    
                    <h3>Navigation Items:</h3>
                    <?php
                    $navItems = getNavigationItems();
                    echo '<pre>' . print_r($navItems, true) . '</pre>';
                    ?>
                    
                    <h3>Social Media Links:</h3>
                    <?php
                    $socialLinks = getSocialMediaLinks();
                    echo '<pre>' . print_r($socialLinks, true) . '</pre>';
                    ?>
                    
                    <h3>Current Page Info:</h3>
                    <ul>
                        <li>Current Page: <?php echo getCurrentPageName(); ?></li>
                        <li>Page Class: <?php echo getPageClass(); ?></li>
                        <li>Is Home Page: <?php echo $isHomePage ? 'Yes' : 'No'; ?></li>
                    </ul>
                </div>
            </div>
        </div>
    </section>
</main>

<style>
.test-section {
    padding: 4rem 0;
    min-height: 60vh;
}

.test-section h1 {
    color: #2c3e50;
    margin-bottom: 1.5rem;
}

.test-section h2 {
    color: #34495e;
    margin-top: 2rem;
    margin-bottom: 1rem;
}

.test-section h3 {
    color: #7f8c8d;
    margin-top: 1.5rem;
    margin-bottom: 0.75rem;
}

.test-section pre {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 0.375rem;
    border: 1px solid #dee2e6;
    overflow-x: auto;
    font-size: 0.875rem;
}

.test-section ul {
    margin-bottom: 1.5rem;
}

.test-section li {
    margin-bottom: 0.5rem;
}
</style>

<?php include 'includes/footer.php'; ?>
