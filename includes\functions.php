<?php
require_once __DIR__ . '/../config/database.php';

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    // Configure session settings for better security and reliability
    ini_set('session.cookie_httponly', 1);
    ini_set('session.use_only_cookies', 1);
    ini_set('session.cookie_secure', 0); // Set to 1 if using HTTPS

    session_start();

    // Regenerate session ID periodically for security
    if (!isset($_SESSION['last_regeneration'])) {
        $_SESSION['last_regeneration'] = time();
    } elseif (time() - $_SESSION['last_regeneration'] > 300) { // 5 minutes
        session_regenerate_id(true);
        $_SESSION['last_regeneration'] = time();
    }
}

// Authentication functions
function isLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

function requireLogin() {
    if (!isLoggedIn()) {
        // Check if we're already in admin directory
        $currentPath = $_SERVER['REQUEST_URI'];
        if (strpos($currentPath, '/admin/') !== false) {
            header('Location: login.php');
        } else {
            header('Location: admin/login.php');
        }
        exit();
    }
}

function login($username, $password) {
    try {
        $database = new Database();
        $conn = $database->getConnection();

        if (!$conn) {
            error_log("Database connection failed in login function");
            return false;
        }

        $stmt = $conn->prepare("SELECT id, username, email, password, role, status FROM users WHERE username = ? OR email = ?");
        $stmt->execute([$username, $username]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($user && password_verify($password, $user['password'])) {
            // Check if user account is active
            if (isset($user['status']) && $user['status'] === 'inactive') {
                return false; // Account is inactive
            }

            $_SESSION['user_id'] = $user['id'];
            $_SESSION['username'] = $user['username'];
            $_SESSION['role'] = $user['role'];
            $_SESSION['login_time'] = time();

            // Update last login time
            try {
                $updateStmt = $conn->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
                $updateStmt->execute([$user['id']]);
            } catch (PDOException $e) {
                // Log error but don't fail login
                error_log("Failed to update last login time: " . $e->getMessage());
            }

            return true;
        }
        return false;
    } catch (Exception $e) {
        error_log("Login function error: " . $e->getMessage());
        return false;
    }
}

function logout() {
    // Clear all session variables
    $_SESSION = array();

    // Delete the session cookie
    if (ini_get("session.use_cookies")) {
        $params = session_get_cookie_params();
        setcookie(session_name(), '', time() - 42000,
            $params["path"], $params["domain"],
            $params["secure"], $params["httponly"]
        );
    }

    // Destroy the session
    session_destroy();

    header('Location: login.php');
    exit();
}

// Utility functions
function sanitizeInput($data) {
    return htmlspecialchars(strip_tags(trim($data)));
}

function uploadFile($file, $uploadDir = 'uploads/') {
    // Validate file upload
    if (!isset($file) || $file['error'] !== UPLOAD_ERR_OK) {
        $errorMessages = [
            UPLOAD_ERR_INI_SIZE => 'File too large (exceeds server limit)',
            UPLOAD_ERR_FORM_SIZE => 'File too large (exceeds form limit)',
            UPLOAD_ERR_PARTIAL => 'File upload incomplete',
            UPLOAD_ERR_NO_FILE => 'No file uploaded',
            UPLOAD_ERR_NO_TMP_DIR => 'Missing temporary folder',
            UPLOAD_ERR_CANT_WRITE => 'Failed to write file to disk',
            UPLOAD_ERR_EXTENSION => 'File upload stopped by extension'
        ];

        $errorCode = $file['error'] ?? UPLOAD_ERR_NO_FILE;
        return ['success' => false, 'message' => $errorMessages[$errorCode] ?? 'Unknown upload error'];
    }

    // Create upload directory if it doesn't exist
    if (!file_exists($uploadDir)) {
        if (!mkdir($uploadDir, 0755, true)) {
            return ['success' => false, 'message' => 'Failed to create upload directory'];
        }
    }

    // Validate file type by extension and MIME type
    $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'mp4', 'mov', 'avi', 'wmv'];
    $allowedMimeTypes = [
        'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp',
        'video/mp4', 'video/quicktime', 'video/x-msvideo', 'video/x-ms-wmv'
    ];

    $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    $fileMimeType = $file['type'];

    if (!in_array($fileExtension, $allowedExtensions)) {
        return ['success' => false, 'message' => 'Invalid file extension. Allowed: ' . implode(', ', $allowedExtensions)];
    }

    if (!in_array($fileMimeType, $allowedMimeTypes)) {
        return ['success' => false, 'message' => 'Invalid file type'];
    }

    // Check file size (max 50MB)
    $maxFileSize = 50 * 1024 * 1024; // 50MB
    if ($file['size'] > $maxFileSize) {
        return ['success' => false, 'message' => 'File too large. Maximum size: 50MB'];
    }

    // Generate secure filename
    $fileName = uniqid('media_', true) . '_' . time() . '.' . $fileExtension;
    $filePath = $uploadDir . $fileName;

    // Additional security: verify file is actually an image/video
    if (strpos($fileMimeType, 'image/') === 0) {
        $imageInfo = getimagesize($file['tmp_name']);
        if ($imageInfo === false) {
            return ['success' => false, 'message' => 'Invalid image file'];
        }
    }

    // Move uploaded file
    if (move_uploaded_file($file['tmp_name'], $filePath)) {
        // Set proper file permissions
        chmod($filePath, 0644);

        return [
            'success' => true,
            'filename' => $fileName,
            'path' => $filePath,
            'relative_path' => str_replace('../', '', $uploadDir) . $fileName,
            'size' => $file['size'],
            'type' => $fileMimeType
        ];
    }

    return ['success' => false, 'message' => 'Failed to move uploaded file'];
}

// Database helper functions
function getServices($status = 'active') {
    $database = new Database();
    $conn = $database->getConnection();
    
    $sql = "SELECT * FROM services";
    if ($status) {
        $sql .= " WHERE status = ?";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$status]);
    } else {
        $stmt = $conn->prepare($sql);
        $stmt->execute();
    }
    
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function getProjects($status = null, $limit = null) {
    $database = new Database();
    $conn = $database->getConnection();

    $sql = "SELECT * FROM projects";
    $params = [];

    if ($status) {
        $sql .= " WHERE status = ?";
        $params[] = $status;
    }

    $sql .= " ORDER BY created_at DESC";

    if ($limit) {
        $sql .= " LIMIT " . (int)$limit;
    }

    $stmt = $conn->prepare($sql);
    $stmt->execute($params);

    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function getProjectById($id) {
    $database = new Database();
    $conn = $database->getConnection();

    $stmt = $conn->prepare("SELECT * FROM projects WHERE id = ?");
    $stmt->execute([$id]);
    $project = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($project && $project['gallery']) {
        $project['gallery'] = json_decode($project['gallery'], true);
    }

    return $project;
}

function getServiceById($id) {
    $database = new Database();
    $conn = $database->getConnection();

    $stmt = $conn->prepare("SELECT * FROM services WHERE id = ? AND status = 'active'");
    $stmt->execute([$id]);

    return $stmt->fetch(PDO::FETCH_ASSOC);
}

function getRelatedProjects($currentProjectId, $limit = 3) {
    $database = new Database();
    $conn = $database->getConnection();

    $limit = (int)$limit; // Ensure it's an integer
    $stmt = $conn->prepare("SELECT * FROM projects WHERE id != ? ORDER BY created_at DESC LIMIT " . $limit);
    $stmt->execute([$currentProjectId]);

    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function getRelatedServices($currentServiceId, $limit = 3) {
    $database = new Database();
    $conn = $database->getConnection();

    $limit = (int)$limit; // Ensure it's an integer
    $stmt = $conn->prepare("SELECT * FROM services WHERE id != ? AND status = 'active' ORDER BY created_at DESC LIMIT " . $limit);
    $stmt->execute([$currentServiceId]);

    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function getMedia($type = null, $limit = null) {
    $database = new Database();
    $conn = $database->getConnection();

    $sql = "SELECT * FROM media";
    $params = [];

    if ($type) {
        $sql .= " WHERE file_type = ?";
        $params[] = $type;
    }

    $sql .= " ORDER BY uploaded_at DESC";

    if ($limit) {
        $sql .= " LIMIT " . (int)$limit;
    }

    $stmt = $conn->prepare($sql);
    $stmt->execute($params);

    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function getMediaById($id) {
    $database = new Database();
    $conn = $database->getConnection();

    $stmt = $conn->prepare("SELECT * FROM media WHERE id = ?");
    $stmt->execute([$id]);

    return $stmt->fetch(PDO::FETCH_ASSOC);
}

function getUserById($id) {
    $database = new Database();
    $conn = $database->getConnection();

    $stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$id]);

    return $stmt->fetch(PDO::FETCH_ASSOC);
}

function getMessages($status = null) {
    $database = new Database();
    $conn = $database->getConnection();

    $sql = "SELECT * FROM messages";
    $params = [];

    if ($status) {
        $sql .= " WHERE status = ?";
        $params[] = $status;
    }

    $sql .= " ORDER BY created_at DESC";

    $stmt = $conn->prepare($sql);
    $stmt->execute($params);

    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function getSetting($key) {
    $database = new Database();
    $conn = $database->getConnection();

    $stmt = $conn->prepare("SELECT setting_value FROM settings WHERE setting_key = ?");
    $stmt->execute([$key]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);

    return $result ? $result['setting_value'] : null;
}

// Header utility functions
function getNavigationItems() {
    return [
        'home' => ['url' => 'index.php', 'label' => 'Home', 'anchor' => '#home'],
        'about' => ['url' => 'about.php', 'label' => 'About Us', 'anchor' => '#about'],
        'services' => ['url' => 'services.php', 'label' => 'Our Services', 'anchor' => '#services'],
        'projects' => [
            'url' => 'projects.php',
            'label' => 'Our Projects',
            'anchor' => '#projects',
            'dropdown' => [
                ['url' => 'projects.php', 'label' => 'All Projects'],
                ['url' => 'projects.php?status=completed', 'label' => 'Completed Projects'],
                ['url' => 'projects.php?status=ongoing', 'label' => 'Ongoing Projects']
            ]
        ],
        'media' => ['url' => 'media.php', 'label' => 'Media', 'anchor' => '#articles'],
        'contact' => ['url' => 'contact.php', 'label' => 'Contact Us', 'anchor' => '#contact']
    ];
}

function isActivePage($url) {
    $currentPage = basename($_SERVER['PHP_SELF']);
    $targetPage = basename($url);
    return $currentPage === $targetPage;
}

function getSocialMediaLinks() {
    return [
        'facebook' => [
            'url' => getSetting('facebook_url') ?: 'https://facebook.com/arcke',
            'icon' => 'fab fa-facebook-f',
            'label' => 'Follow us on Facebook'
        ],
        'twitter' => [
            'url' => getSetting('twitter_url') ?: 'https://twitter.com/arcke',
            'icon' => 'fab fa-twitter',
            'label' => 'Follow us on Twitter'
        ],
        'linkedin' => [
            'url' => getSetting('linkedin_url') ?: 'https://linkedin.com/company/arcke',
            'icon' => 'fab fa-linkedin-in',
            'label' => 'Connect with us on LinkedIn'
        ],
        'pinterest' => [
            'url' => getSetting('pinterest_url') ?: 'https://pinterest.com/arcke',
            'icon' => 'fab fa-pinterest-p',
            'label' => 'Follow us on Pinterest'
        ]
    ];
}

function renderNavigation($navItems, $isHomePage = false) {
    $html = '<ul>';

    foreach ($navItems as $key => $item) {
        $isActive = isActivePage($item['url']);
        $activeClass = $isActive ? ' aria-current="page"' : '';
        $href = $isHomePage && isset($item['anchor']) ? $item['anchor'] : $item['url'];

        if (isset($item['dropdown'])) {
            $html .= '<li class="nav-dropdown">';
            $html .= '<a href="' . $href . '" class="dropdown-toggle" aria-expanded="false"' . $activeClass . '>';
            $html .= $item['label'] . ' <i class="fas fa-chevron-down dropdown-icon" aria-hidden="true"></i>';
            $html .= '</a>';
            $html .= '<ul class="dropdown-menu" role="menu">';

            foreach ($item['dropdown'] as $dropdownItem) {
                $html .= '<li><a href="' . $dropdownItem['url'] . '" role="menuitem">' . $dropdownItem['label'] . '</a></li>';
            }

            $html .= '</ul>';
            $html .= '</li>';
        } else {
            $html .= '<li><a href="' . $href . '"' . $activeClass . '>' . $item['label'] . '</a></li>';
        }
    }

    $html .= '</ul>';
    return $html;
}

function renderSocialIcons($socialLinks, $vertical = false) {
    $containerClass = $vertical ? 'social-icons-vertical' : 'footer-socials';
    $html = '<div class="' . $containerClass . '" ' . ($vertical ? 'aria-label="Social media links"' : 'role="list" aria-label="Social media links"') . '>';

    foreach ($socialLinks as $platform => $link) {
        $html .= '<a href="' . $link['url'] . '" aria-label="' . $link['label'] . '" target="_blank" rel="noopener noreferrer">';
        $html .= '<i class="' . $link['icon'] . '" aria-hidden="true"></i>';
        $html .= '</a>';
    }

    $html .= '</div>';
    return $html;
}



function getCurrentPageName() {
    return basename($_SERVER['PHP_SELF'], '.php');
}

function getPageClass() {
    $pageName = getCurrentPageName();
    return 'page-' . str_replace('_', '-', $pageName);
}

function updateSetting($key, $value) {
    $database = new Database();
    $conn = $database->getConnection();
    
    $stmt = $conn->prepare("INSERT INTO settings (setting_key, setting_value) VALUES (?, ?) ON DUPLICATE KEY UPDATE setting_value = ?");
    return $stmt->execute([$key, $value, $value]);
}

// API response helper
function jsonResponse($data, $status = 200) {
    http_response_code($status);
    header('Content-Type: application/json');
    echo json_encode($data);
    exit();
}

// Pagination helper
function paginate($table, $page = 1, $perPage = 10, $conditions = '') {
    $database = new Database();
    $conn = $database->getConnection();

    $offset = ($page - 1) * $perPage;

    // Get total count
    $countSql = "SELECT COUNT(*) as total FROM $table $conditions";
    $stmt = $conn->prepare($countSql);
    $stmt->execute();
    $total = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

    // Get data
    $dataSql = "SELECT * FROM $table $conditions LIMIT " . (int)$perPage . " OFFSET " . (int)$offset;
    $stmt = $conn->prepare($dataSql);
    $stmt->execute();
    $data = $stmt->fetchAll(PDO::FETCH_ASSOC);

    return [
        'data' => $data,
        'total' => $total,
        'page' => $page,
        'perPage' => $perPage,
        'totalPages' => ceil($total / $perPage)
    ];
}

// Generate breadcrumbs
function generateBreadcrumbs($items) {
    $breadcrumbs = '<nav aria-label="breadcrumb"><ol class="breadcrumb">';
    foreach ($items as $item) {
        if (isset($item['url'])) {
            $breadcrumbs .= '<li class="breadcrumb-item"><a href="' . $item['url'] . '">' . $item['title'] . '</a></li>';
        } else {
            $breadcrumbs .= '<li class="breadcrumb-item active">' . $item['title'] . '</li>';
        }
    }
    $breadcrumbs .= '</ol></nav>';
    return $breadcrumbs;
}

// Get testimonials (if testimonials table exists)
function getTestimonials($status = 'active', $limit = null, $featured_only = false) {
    try {
        $database = new Database();
        $conn = $database->getConnection();

        $sql = "SELECT * FROM testimonials";
        $params = [];
        $conditions = [];

        if ($status) {
            $conditions[] = "status = ?";
            $params[] = $status;
        }

        if ($featured_only) {
            $conditions[] = "featured = 1";
        }

        if (!empty($conditions)) {
            $sql .= " WHERE " . implode(" AND ", $conditions);
        }

        $sql .= " ORDER BY display_order ASC, created_at DESC";

        if ($limit) {
            $sql .= " LIMIT " . (int)$limit;
        }

        $stmt = $conn->prepare($sql);
        $stmt->execute($params);

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        // Table might not exist, return empty array
        error_log("getTestimonials error: " . $e->getMessage());
        return [];
    }
}

// Get testimonial by ID
function getTestimonialById($id) {
    try {
        $database = new Database();
        $conn = $database->getConnection();

        $stmt = $conn->prepare("SELECT * FROM testimonials WHERE id = ?");
        $stmt->execute([$id]);

        return $stmt->fetch(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        error_log("getTestimonialById error: " . $e->getMessage());
        return false;
    }
}

// Add new testimonial
function addTestimonial($data) {
    try {
        $database = new Database();
        $conn = $database->getConnection();

        $stmt = $conn->prepare("INSERT INTO testimonials (client_name, client_position, client_company, client_image, testimonial_text, rating, status, featured, display_order) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");

        return $stmt->execute([
            $data['client_name'],
            $data['client_position'],
            $data['client_company'],
            $data['client_image'],
            $data['testimonial_text'],
            $data['rating'],
            $data['status'],
            $data['featured'] ? 1 : 0,
            $data['display_order']
        ]);
    } catch (PDOException $e) {
        error_log("addTestimonial error: " . $e->getMessage());
        return false;
    }
}

// Update testimonial
function updateTestimonial($id, $data) {
    try {
        $database = new Database();
        $conn = $database->getConnection();

        $stmt = $conn->prepare("UPDATE testimonials SET client_name = ?, client_position = ?, client_company = ?, client_image = ?, testimonial_text = ?, rating = ?, status = ?, featured = ?, display_order = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?");

        return $stmt->execute([
            $data['client_name'],
            $data['client_position'],
            $data['client_company'],
            $data['client_image'],
            $data['testimonial_text'],
            $data['rating'],
            $data['status'],
            $data['featured'] ? 1 : 0,
            $data['display_order'],
            $id
        ]);
    } catch (PDOException $e) {
        error_log("updateTestimonial error: " . $e->getMessage());
        return false;
    }
}

// Delete testimonial
function deleteTestimonial($id) {
    try {
        $database = new Database();
        $conn = $database->getConnection();

        $stmt = $conn->prepare("DELETE FROM testimonials WHERE id = ?");
        return $stmt->execute([$id]);
    } catch (PDOException $e) {
        error_log("deleteTestimonial error: " . $e->getMessage());
        return false;
    }
}

// Toggle testimonial status
function toggleTestimonialStatus($id) {
    try {
        $database = new Database();
        $conn = $database->getConnection();

        $stmt = $conn->prepare("UPDATE testimonials SET status = CASE WHEN status = 'active' THEN 'inactive' ELSE 'active' END WHERE id = ?");
        return $stmt->execute([$id]);
    } catch (PDOException $e) {
        error_log("toggleTestimonialStatus error: " . $e->getMessage());
        return false;
    }
}

// Get recent articles/blog posts (if articles table exists)
function getArticles($limit = null) {
    try {
        $database = new Database();
        $conn = $database->getConnection();

        $sql = "SELECT * FROM articles WHERE status = 'published' ORDER BY created_at DESC";

        if ($limit) {
            $sql .= " LIMIT " . (int)$limit;
        }

        $stmt = $conn->prepare($sql);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        // Table might not exist, return empty array
        return [];
    }
}

// Get company statistics (if stats table exists)
function getCompanyStats() {
    try {
        $database = new Database();
        $conn = $database->getConnection();

        // Try to get stats from a dedicated table first
        $stmt = $conn->prepare("SELECT * FROM company_stats ORDER BY id DESC LIMIT 1");
        $stmt->execute();
        $stats = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($stats) {
            return $stats;
        }

        // Fallback: calculate stats from existing data
        $projectsCount = $conn->query("SELECT COUNT(*) FROM projects")->fetchColumn();
        $servicesCount = $conn->query("SELECT COUNT(*) FROM services WHERE status = 'active'")->fetchColumn();

        return [
            'projects_completed' => $projectsCount,
            'services_offered' => $servicesCount,
            'years_experience' => 15, // Default value
            'happy_clients' => $projectsCount * 2 // Estimate
        ];
    } catch (PDOException $e) {
        // Return default stats if tables don't exist
        return [
            'projects_completed' => 100,
            'services_offered' => 6,
            'years_experience' => 15,
            'happy_clients' => 200
        ];
    }
}
?>
