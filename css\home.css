/* ==========================================================================
   CSS Reset & Global Styles
   ========================================================================== */

:root {
    /* Color Palette */
    --primary-color: #c4a265;
    --dark-bg: #141414;
    --section-dark-bg: #101010;
    --services-articles-bg: #141318;
    --card-bg: #1d1b25;
    --text-color: #d1d1d1;
    --heading-color: #ffffff;
    --border-color: #333333;

    /* Typography */
    --font-heading: 'Josefin Sans', sans-serif;
    --font-body: 'Roboto', sans-serif;

    /* Spacing */
    --section-padding: 120px;
    --container-max-width: 1200px;

    /* Transitions */
    --transition-fast: 0.3s ease;
    --transition-medium: 0.4s ease;
    --transition-slow: 0.5s ease;

    /* Z-index layers */
    --z-header: 100;
    --z-mobile-nav: 99;
    --z-overlay: 98;
    --z-modal: 1000;
}

/* ==========================================================================
   Base Styles
   ========================================================================== */

*,
*::before,
*::after {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: var(--font-body);
    background-color: var(--dark-bg);
    color: var(--text-color);
    font-size: 1rem;
    line-height: 1.7;
    overflow-x: hidden;
}

/* ==========================================================================
   Accessibility & Utility Classes
   ========================================================================== */

/* Screen reader only content */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus styles for better accessibility */
*:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Skip to main content link */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--primary-color);
    color: white;
    padding: 8px;
    text-decoration: none;
    z-index: var(--z-modal);
    transition: top var(--transition-fast);
}

.skip-link:focus {
    top: 6px;
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {

    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* ==========================================================================
   Typography
   ========================================================================== */

h1,
h2,
h3,
h4,
h5,
h6 {
    font-family: var(--font-heading);
    color: var(--heading-color);
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 1rem;
}

h1 {
    font-size: clamp(2.5rem, 5vw, 5.3rem);
    font-weight: 700;
}

h2 {
    font-size: clamp(2rem, 4vw, 3rem);
}

h3 {
    font-size: clamp(1.5rem, 3vw, 1.8rem);
}

h4 {
    font-size: clamp(1.25rem, 2.5vw, 1.4rem);
}

p {
    margin-bottom: 1rem;
}

/* ==========================================================================
   Links & Lists
   ========================================================================== */

a {
    text-decoration: none;
    color: inherit;
    transition: color var(--transition-fast);
}

a:hover,
a:focus {
    color: var(--primary-color);
}

ul,
ol {
    list-style: none;
}

/* ==========================================================================
   Images & Media
   ========================================================================== */

img {
    max-width: 100%;
    height: auto;
    display: block;
}

/* ==========================================================================
   Layout Components
   ========================================================================== */

.container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 1rem;
}

@media (min-width: 768px) {
    .container {
        padding: 0 2rem;
    }
}

.section-padding {
    padding: var(--section-padding) 0;
}

/* Section headers */
.projects-header,
.articles-header {
    text-align: center;
    margin-bottom: 3rem;
}

/* About actions layout */
.about-actions {
    display: flex;
    align-items: flex-end;
    gap: 2rem;
    flex-wrap: wrap;
    margin-top: 2rem;
}

/* ==========================================================================
   Section Components
   ========================================================================== */

.section-subtitle {
    font-family: var(--font-body);
    color: var(--primary-color);
    font-size: 1rem;
    font-weight: 500;
    margin-bottom: 1rem;
    letter-spacing: 2px;
    text-transform: uppercase;
    display: block;
}

.section-title {
    font-size: clamp(2rem, 4vw, 3rem);
    margin-bottom: 2.5rem;
    line-height: 1.2;
}

/* Hero description */
.hero-description {
    font-size: 1.125rem;
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    opacity: 0.9;
}

/* CTA description */
.cta-description {
    font-size: 1.125rem;
    margin-top: 1rem;
    opacity: 0.9;
}

/* ==========================================================================
   Button Components
   ========================================================================== */

.btn {
    display: inline-block;
    padding: 0.75rem 1.75rem;
    font-family: var(--font-heading);
    font-weight: 600;
    font-size: 0.8125rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    border: 2px solid transparent;
    border-radius: 0;
    transition: all var(--transition-medium);
    cursor: pointer;
    text-align: center;
    line-height: 1;
    position: relative;
    overflow: hidden;
}

.btn:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

.btn-primary {
    background-color: var(--primary-color);
    color: #fff;
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: transparent;
    color: var(--primary-color);
}

.btn-dark {
    background-color: transparent;
    color: #fff;
    border: 2px solid #fff;
}

.btn-dark:hover {
    background-color: #fff;
    border-color: #fff;
    color: #0e0e0e;
}

.btn-quote {
    background-color: var(--primary-color);
    color: #fff;
    border-color: var(--primary-color);
    padding: 12px 28px;
    font-size: 13px;
    font-weight: 600;
    letter-spacing: 1px;
    text-transform: uppercase;
    border-radius: 0;
    transition: all 0.3s ease;
}

.btn-quote:hover {
    background-color: transparent;
    color: var(--primary-color);
    border-color: var(--primary-color);
}

/* --- Header --- */
.main-header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 100;
    padding: 20px 0;
    background-color: rgba(52, 58, 64, 0.95);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 50px;
}

.main-header.scrolled {
    background-color: rgba(52, 58, 64, 0.98);
    padding: 15px 0;
}

.logo img {
    max-width: 160px;
    height: auto;
    transition: transform 0.3s ease;
}

.logo:hover img {
    transform: scale(1.05);
}

.main-nav ul {
    display: flex;
    gap: 40px;
}

.main-nav a {
    font-family: var(--font-heading);
    font-weight: 500;
    color: #fff;
    font-size: 16px;
    transition: color 0.3s ease;
    position: relative;
}

.main-nav a:hover {
    color: var(--primary-color);
}

.main-nav a::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--primary-color);
    transition: width 0.3s ease;
}

.main-nav a:hover::after {
    width: 100%;
}

/* Dropdown Navigation */
.nav-dropdown {
    position: relative;
}

.dropdown-toggle {
    display: flex;
    align-items: center;
    gap: 5px;
}

.dropdown-icon {
    font-size: 12px;
    transition: transform var(--transition-fast);
}

.nav-dropdown:hover .dropdown-icon {
    transform: rotate(180deg);
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background-color: rgba(52, 58, 64, 0.98);
    backdrop-filter: blur(10px);
    min-width: 200px;
    padding: 15px 0;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all var(--transition-fast);
    border: 1px solid rgba(255, 255, 255, 0.1);
    z-index: 1000;
}

.nav-dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-menu li {
    padding: 0;
}

.dropdown-menu a {
    display: block;
    padding: 12px 20px;
    color: #d1d1d1;
    font-size: 14px;
    font-weight: 400;
    transition: all var(--transition-fast);
    border-left: 3px solid transparent;
    position: relative;
}

.dropdown-menu a::after {
    display: none;
}

.dropdown-menu a:hover {
    color: var(--primary-color);
    background-color: rgba(255, 255, 255, 0.05);
    border-left-color: var(--primary-color);
}

.mobile-nav-toggle {
    display: none;
    background: none;
    border: none;
    color: #fff;
    font-size: 28px;
    cursor: pointer;
    transition: color 0.3s ease;
}

.mobile-nav-toggle:hover {
    color: var(--primary-color);
}

.mobile-nav-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
    z-index: 98;
}

.mobile-nav-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* --- Hero Section --- */
.hero-section {
    height: 100vh;
    min-height: 800px;
    background: url('../images/front.jpg') no-repeat center center/cover;
    display: flex;
    align-items: center;
    position: relative;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(14, 14, 14, 0.4);
}

.hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
    color: #fff;
}

.hero-content .section-subtitle {
    color: #fff;
}

.hero-content h1 {
    font-size: clamp(3rem, 6vw, 5.3rem);
    font-weight: 700;
    line-height: 1.1;
    margin-bottom: 1.5rem;
}

.hero-content .hero-description {
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 2.5rem;
}

.hero-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
}

.social-icons-vertical {
    position: absolute;
    left: 50px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
    gap: 10px;
    z-index: 5;
}

.social-icons-vertical a {
    color: #fff;
    font-size: 14px;
    background: rgba(255, 255, 255, 0.05);
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.social-icons-vertical a:hover {
    background: var(--primary-color);
    color: #fff;
}

.scroll-indicator {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 50px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    background: transparent;
    display: flex;
    justify-content: center;
    align-items: center;
    color: rgba(255, 255, 255, 0.7);
    font-size: 16px;
    animation: bounce 2s infinite;
    z-index: 5;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.scroll-indicator:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

@keyframes bounce {

    0%,
    20%,
    50%,
    80%,
    100% {
        transform: translateX(-50%) translateY(0);
    }

    40% {
        transform: translateX(-50%) translateY(-10px);
    }

    60% {
        transform: translateX(-50%) translateY(-5px);
    }
}

/* --- About Section --- */
.about-section {
    background-color: var(--section-dark-bg);
}

.about-section .container {
    display: flex;
    align-items: center;
    gap: 80px;
}

.about-section .section-subtitle {
    color: var(--primary-color);
}

.about-section .section-title {
    color: var(--heading-color);
}

.about-section p {
    color: var(--text-color);
}

.about-images {
    flex: 0 0 50%;
    position: relative;
}

.about-images .img-1 {
    width: 80%;
    border: 15px solid var(--card-bg);
}

.about-images .img-2 {
    width: 65%;
    position: absolute;
    bottom: -60px;
    right: 0;
    border: 15px solid var(--card-bg);
}

.about-content {
    flex: 1;
}

.about-content p {
    margin-bottom: 25px;
}

.about-checklist {
    list-style: none;
    margin-bottom: 30px;
}

.about-checklist li {
    margin-bottom: 12px;
    padding-left: 25px;
    position: relative;
}

.about-checklist li::before {
    content: '\f00c';
    font-family: "Font Awesome 6 Free";
    font-weight: 900;
    position: absolute;
    left: 0;
    top: 6px;
    color: var(--primary-color);
    font-size: 14px;
}

.experience-box {
    background: url('../images/hero2.jpg') no-repeat center center/cover;
    padding: 25px 35px;
    display: inline-block;
    text-align: center;
    border: 10px solid var(--card-bg);
    box-shadow: 0px 10px 30px 0px rgba(0, 0, 0, 0.1);
}

.experience-box .years {
    font-size: 50px;
    font-family: var(--font-heading);
    font-weight: 700;
    color: #fff;
    line-height: 1;
}

.experience-box .text {
    color: #fff;
    font-weight: 500;
    font-size: 16px;
}

.about-content .btn-primary {
    margin-top: 40px;
}

/* --- Services Section --- */
.services-section {
    background-color: var(--services-articles-bg);
}

.services-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    margin-bottom: 60px;
}

.services-header-content {
    flex: 1;
}

.services-header-button {
    flex-shrink: 0;
}

.btn-services {
    background-color: var(--primary-color);
    color: #fff;
    padding: 18px 36px;
    font-size: 14px;
    font-weight: 600;
    letter-spacing: 1.5px;
    text-transform: uppercase;
    border: 2px solid var(--primary-color);
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    border-radius: 0;
}

.btn-services:hover {
    background-color: transparent;
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
}

.service-card {
    background-color: var(--card-bg);
    padding: 50px 40px;
    text-align: left;
    position: relative;
    overflow: hidden;
    transition: transform 0.3s ease, border 0.3s ease;
    border: 1px solid transparent;
}

.service-card:hover {
    transform: translateY(-10px);
    border-color: var(--primary-color);
}

.service-card .service-icon {
    margin-bottom: 25px;
}

.service-card .service-icon i {
    font-size: 50px;
    color: var(--primary-color);
}

.service-card .service-icon img {
    width: 50px;
    height: 50px;
}

.service-card h3 {
    font-size: 24px;
    margin-bottom: 15px;
}

.service-card .service-number {
    position: absolute;
    top: 20px;
    right: 30px;
    font-size: 100px;
    font-weight: 700;
    color: rgba(255, 255, 255, 0.03);
    line-height: 1;
}

/* --- Projects Section --- */
.projects-section {
    background-color: var(--section-dark-bg);
}

.projects-section .section-subtitle {
    color: var(--primary-color);
}

.projects-section .section-title {
    color: var(--heading-color);
}

.projects-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-auto-rows: 300px;
    gap: 30px;
}

.project-item {
    overflow: hidden;
    position: relative;
}

.project-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.project-item:hover img {
    transform: scale(1.1);
}

.project-item .project-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(14, 14, 14, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transition: opacity 0.4s ease;
}

.project-item:hover .project-overlay {
    opacity: 1;
}

.project-view-btn {
    width: 60px;
    height: 60px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 20px;
    transform: scale(0.8);
    transition: transform var(--transition-medium);
    cursor: pointer;
}

.project-item:hover .project-view-btn {
    transform: scale(1);
}

.project-view-btn:focus {
    outline: 2px solid white;
    outline-offset: 2px;
}

.project-item:nth-child(1) {
    grid-row: span 1;
}

.project-item:nth-child(2) {
    grid-column: 2 / 3;
    grid-row: 1 / 3;
}

.project-item:nth-child(3) {
    grid-row: span 1;
}

.project-item:nth-child(4) {
    grid-row: span 1;
}

.project-item:nth-child(5) {
    grid-row: span 1;
}

/* --- CTA Section --- */
.cta-section {
    background: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), url('../images/header-1-scaled.jpg') no-repeat center center/cover;
    background-attachment: fixed;
    padding: 120px 0;
    position: relative;
    display: flex;
    align-items: center;
}

.cta-section .container {
    position: relative;
    z-index: 2;
    width: 100%;
}

.cta-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 50px;
}

.cta-text {
    flex: 1;
    text-align: left;
}

.cta-section .section-subtitle {
    color: var(--primary-color);
    font-size: 14px;
    font-weight: 600;
    letter-spacing: 2px;
    text-transform: uppercase;
    margin-bottom: 20px;
    display: block;
}

.cta-section .section-title {
    font-size: 48px;
    line-height: 1.2;
    margin-bottom: 0;
    color: #fff;
    font-weight: 700;
}

.cta-button {
    flex-shrink: 0;
}

.btn-cta {
    background-color: var(--primary-color);
    color: #fff;
    padding: 12px 28px;
    font-size: 14px;
    font-weight: 600;
    letter-spacing: 1px;
    text-transform: uppercase;
    border: 2px solid var(--primary-color);
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.btn-cta:hover {
    background-color: transparent;
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.cta-scroll-indicator {
    position: absolute;
    bottom: 30px;
    right: 50%;
    transform: translateX(50%);
    width: 50px;
    height: 50px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: rgba(255, 255, 255, 0.7);
    font-size: 16px;
    animation: bounce 2s infinite;
}

@keyframes bounce {

    0%,
    20%,
    50%,
    80%,
    100% {
        transform: translateX(50%) translateY(0);
    }

    40% {
        transform: translateX(50%) translateY(-10px);
    }

    60% {
        transform: translateX(50%) translateY(-5px);
    }
}

/* --- Testimonials Section --- */
.testimonials-section {
    background-color: var(--section-dark-bg);
}

.testimonial-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    margin-bottom: 50px;
}

.testimonial-nav {
    display: flex;
    gap: 10px;
}

.swiper-button-prev,
.swiper-button-next {
    position: relative;
    width: 50px;
    height: 50px;
    background-color: var(--card-bg);
    color: var(--primary-color);
    transition: all 0.3s ease;
    margin: 0;
}

.swiper-button-prev:hover,
.swiper-button-next:hover {
    background-color: var(--primary-color);
    color: #fff;
}

.swiper-button-prev::after,
.swiper-button-next::after {
    font-size: 16px;
    font-weight: 900;
}

.testimonial-card {
    background-color: var(--card-bg);
    padding: 50px 40px;
    text-align: left;
    position: relative;
}

.testimonial-card::after {
    content: '';
    position: absolute;
    bottom: -20px;
    left: 50px;
    width: 0;
    height: 0;
    border-left: 20px solid transparent;
    border-right: 20px solid transparent;
    border-top: 20px solid var(--card-bg);
}

.testimonial-card::before {
    content: "\f10d";
    font-family: "Font Awesome 6 Free";
    font-weight: 900;
    position: absolute;
    top: 30px;
    right: 30px;
    font-size: 80px;
    color: rgba(255, 255, 255, 0.04);
    z-index: 0;
    line-height: 1;
}

.testimonial-card-content {
    position: relative;
    z-index: 1;
}

.testimonial-rating {
    color: #f39c12;
    margin-bottom: 20px;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-top: 30px;
}

.testimonial-author img {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    border: 3px solid var(--primary-color);
}

.author-info h4 {
    font-size: 20px;
    margin-bottom: 0px;
}

.author-info span {
    font-size: 14px;
    color: var(--primary-color);
}

/* --- Articles Section --- */
.articles-section {
    background-color: var(--services-articles-bg);
}

.articles-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
}

.article-card {
    background-color: var(--card-bg);
}

.article-content {
    padding: 30px;
}

.article-meta {
    display: flex;
    gap: 20px;
    margin-bottom: 15px;
    font-size: 14px;
    color: #aaa;
}

.article-meta i {
    margin-right: 5px;
    color: var(--primary-color);
}

.article-card h3 {
    font-size: 22px;
    margin-bottom: 20px;
    line-height: 1.4;
    transition: color 0.3s ease;
}

.article-card:hover h3 {
    color: var(--primary-color);
}

.article-card .read-more-arrow {
    width: 40px;
    height: 40px;
    background-color: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    transition: background-color 0.3s ease;
}

.article-card:hover .read-more-arrow {
    background-color: #fff;
    color: var(--primary-color);
}

.article-image {
    overflow: hidden;
}

.article-image img {
    transition: transform 0.5s ease;
}

.article-card:hover .article-image img {
    transform: scale(1.1);
}

/* --- Newsletter Section --- */
.newsletter-section {
    background-color: var(--section-dark-bg);
    padding: 80px 0;
    border-bottom: 1px solid var(--border-color);
}

.newsletter-section .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.newsletter-section h3 {
    color: var(--heading-color);
}

.newsletter-section p {
    color: var(--text-color);
}

.newsletter-content h3 {
    font-size: 28px;
    margin-bottom: 5px;
    color: var(--heading-color);
}

.newsletter-form {
    display: flex;
    width: 45%;
}

.newsletter-form input {
    flex: 1;
    padding: 16px 20px;
    border: 1px solid var(--border-color);
    background-color: var(--card-bg);
    color: var(--text-color);
    font-family: var(--font-body);
}

.newsletter-form input:focus {
    outline: none;
    border-color: var(--primary-color);
}

.newsletter-form input::placeholder {
    color: #999;
}

.newsletter-form .btn {
    border-radius: 0;
    border-width: 1px;
}

/* --- Footer --- */
.main-footer {
    background-color: var(--section-dark-bg);
    padding-top: 120px;
}

.footer-widgets {
    display: grid;
    grid-template-columns: 2.5fr 1fr 1.5fr 2fr;
    gap: 30px;
    padding-bottom: 80px;
    border-bottom: 1px solid var(--border-color);
}

.footer-widget h4 {
    font-size: 22px;
    margin-bottom: 30px;
}

.footer-widget .logo {
    margin-bottom: 25px;
}

.footer-socials {
    display: flex;
    gap: 10px;
    margin-top: 25px;
}

.footer-socials a {
    width: 45px;
    height: 45px;
    background-color: var(--card-bg);
    display: flex;
    justify-content: center;
    align-items: center;
}

.footer-list li {
    margin-bottom: 15px;
}

.footer-list a {
    display: flex;
    align-items: center;
    gap: 10px;
}

.footer-list i {
    color: var(--primary-color);
    transition: transform 0.3s ease, color 0.3s ease;
}

.footer-list a:hover i {
    transform: translateX(5px);
    color: #fff;
}

.contact-info div {
    margin-bottom: 15px;
    padding-left: 30px;
    position: relative;
}

.contact-info a {
    color: var(--text-color);
    transition: color var(--transition-fast);
}

.contact-info a:hover,
.contact-info a:focus {
    color: var(--primary-color);
}

.contact-info strong {
    color: var(--primary-color);
}

.contact-info div::before {
    font-family: "Font Awesome 6 Free";
    font-weight: 900;
    position: absolute;
    left: 0;
    top: 2px;
    color: var(--primary-color);
}

.contact-info .phone::before {
    content: "\f095";
}

.contact-info .email::before {
    content: "\f0e0";
}

.contact-info .location::before {
    content: "\f3c5";
}

.copyright-area {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30px 0;
}

.back-to-top {
    width: 50px;
    height: 50px;
    background-color: var(--primary-color);
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 18px;
}

.footer_section_five .row.pb-bg {
    padding: 0 0 65px;
    border-bottom: 1px solid #2B2B2B;
    margin-bottom: 90px;
}

/* --- Responsive Design --- */
@media (max-width: 1200px) {
    .footer-widgets {
        grid-template-columns: repeat(2, 1fr);
        gap: 50px;
    }
}

@media (max-width: 992px) {
    .section-padding {
        padding: 100px 0;
    }

    .section-title {
        font-size: 40px;
    }

    .hero-content h1 {
        font-size: 60px;
    }

    .main-nav {
        position: fixed;
        top: 0;
        left: -100%;
        width: 300px;
        height: 100vh;
        background-color: rgba(52, 58, 64, 0.98);
        backdrop-filter: blur(10px);
        padding: 100px 30px 30px;
        transition: left 0.3s ease;
        z-index: 99;
    }

    .main-nav.active {
        left: 0;
    }

    .main-nav ul {
        flex-direction: column;
        gap: 20px;
    }

    .main-nav a {
        font-size: 18px;
        padding: 10px 0;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .header-container .btn-quote {
        display: none;
    }

    .mobile-nav-toggle {
        display: block;
        z-index: 101;
    }

    /* Mobile Dropdown Styles */
    .nav-dropdown .dropdown-menu {
        position: static;
        background: none;
        box-shadow: none;
        border: none;
        padding: 0;
        margin-left: 20px;
        opacity: 1;
        visibility: visible;
        transform: none;
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease;
    }

    .nav-dropdown.active .dropdown-menu {
        max-height: 200px;
    }

    .dropdown-menu a {
        padding: 10px 0;
        font-size: 16px;
        border-left: none;
        border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    }

    .dropdown-toggle {
        justify-content: space-between;
        width: 100%;
    }

    .about-section .container {
        flex-direction: column;
        text-align: center;
    }

    .about-images {
        margin-bottom: 80px;
        flex-basis: auto;
    }

    .about-checklist li {
        padding-left: 0;
        text-align: left;
    }

    .about-checklist li::before {
        display: none;
    }

    /* Hide icon, simple list on mobile */

    .services-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 30px;
        text-align: center;
    }

    .services-header-content {
        text-align: center;
        width: 100%;
    }

    .services-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .projects-grid {
        grid-template-columns: 1fr 1fr;
        grid-auto-rows: 250px;
    }

    .project-item:nth-child(2) {
        grid-column: auto;
        grid-row: auto;
    }

    .testimonial-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .testimonial-nav {
        margin-top: 20px;
    }

    .articles-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .newsletter-section .container {
        flex-direction: column;
        gap: 30px;
        text-align: center;
    }

    .newsletter-form {
        width: 100%;
        max-width: 500px;
    }

    .cta-content {
        flex-direction: column;
        text-align: center;
        gap: 30px;
    }

    .cta-text {
        text-align: center;
    }

    .cta-section .section-title {
        font-size: 40px;
    }
}

@media (max-width: 768px) {
    .section-padding {
        padding: 80px 0;
    }

    .section-title {
        font-size: 36px;
    }

    .hero-content h1 {
        font-size: 48px;
    }

    .social-icons-vertical {
        display: none;
    }

    .services-header {
        text-align: center;
    }

    .services-grid {
        grid-template-columns: 1fr;
    }

    .projects-grid {
        grid-template-columns: 1fr;
    }

    .articles-grid {
        grid-template-columns: 1fr;
    }

    .footer-widgets {
        grid-template-columns: 1fr;
    }

    .testimonial-header {
        align-items: center;
    }

    .testimonial-header .section-title {
        text-align: center;
        margin-bottom: 20px;
    }

    .copyright-area {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }

    .cta-section {
        padding: 80px 0;
        min-height: 400px;
    }

    .cta-section .section-title {
        font-size: 32px;
    }

    .cta-scroll-indicator {
        display: none;
    }
}