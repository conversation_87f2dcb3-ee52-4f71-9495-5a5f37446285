<?php
require_once '../includes/functions.php';

// Check if user is logged in
requireLogin();

// Set page variables
$pageTitle = 'Edit Testimonial';
$current_page = 'testimonials.php';

// Get testimonial ID from URL
$testimonialId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$testimonialId) {
    header('Location: testimonials.php');
    exit();
}

// Get testimonial details
$testimonial = getTestimonialById($testimonialId);

if (!$testimonial) {
    header('Location: testimonials.php');
    exit();
}

$message = '';
$messageType = '';

// Handle form submission
if ($_POST) {
    $client_name = sanitizeInput($_POST['client_name']);
    $client_position = sanitizeInput($_POST['client_position']);
    $client_company = sanitizeInput($_POST['client_company']);
    $testimonial_text = sanitizeInput($_POST['testimonial_text']);
    $rating = (int)$_POST['rating'];
    $status = sanitizeInput($_POST['status']);
    $featured = isset($_POST['featured']) ? 1 : 0;
    $display_order = (int)$_POST['display_order'];
    
    if ($client_name && $testimonial_text && $rating >= 1 && $rating <= 5) {
        // Handle client image upload
        $clientImage = $testimonial['client_image']; // Keep existing image by default
        
        if (isset($_FILES['client_image']) && $_FILES['client_image']['error'] === UPLOAD_ERR_OK) {
            $uploadResult = uploadFile($_FILES['client_image'], '../uploads/avatars/');
            if ($uploadResult['success']) {
                // Delete old image if it exists
                if ($testimonial['client_image'] && file_exists('../' . $testimonial['client_image'])) {
                    unlink('../' . $testimonial['client_image']);
                }
                $clientImage = $uploadResult['relative_path'];
            } else {
                $message = 'Image upload failed: ' . $uploadResult['message'];
                $messageType = 'error';
            }
        }
        
        // Handle image removal
        if (isset($_POST['remove_image']) && $_POST['remove_image'] == '1') {
            if ($testimonial['client_image'] && file_exists('../' . $testimonial['client_image'])) {
                unlink('../' . $testimonial['client_image']);
            }
            $clientImage = null;
        }
        
        if (!$message) {
            $testimonialData = [
                'client_name' => $client_name,
                'client_position' => $client_position,
                'client_company' => $client_company,
                'client_image' => $clientImage,
                'testimonial_text' => $testimonial_text,
                'rating' => $rating,
                'status' => $status,
                'featured' => $featured,
                'display_order' => $display_order
            ];
            
            if (updateTestimonial($testimonialId, $testimonialData)) {
                $message = 'Testimonial updated successfully!';
                $messageType = 'success';
                
                // Refresh testimonial data
                $testimonial = getTestimonialById($testimonialId);
            } else {
                $message = 'Failed to update testimonial. Please try again.';
                $messageType = 'error';
            }
        }
    } else {
        $message = 'Please fill in all required fields and ensure rating is between 1-5.';
        $messageType = 'error';
    }
}

include 'includes/admin_header.php';
?>

<!-- Edit Testimonial Content -->
<div class="page-header d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="mb-1">Edit Testimonial</h2>
        <p class="text-muted mb-0">Update testimonial information for <?php echo htmlspecialchars($testimonial['client_name']); ?>.</p>
    </div>
    <div class="d-flex gap-2">
        <a href="testimonials.php" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> Back to Testimonials
        </a>
        <button type="button" class="btn btn-outline-danger" onclick="deleteTestimonial()">
            <i class="fas fa-trash"></i> Delete
        </button>
    </div>
</div>

<!-- Display Messages -->
<?php if ($message): ?>
    <div class="alert alert-<?php echo $messageType === 'success' ? 'success' : 'danger'; ?> alert-dismissible fade show" role="alert">
        <i class="fas fa-<?php echo $messageType === 'success' ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
        <?php echo htmlspecialchars($message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<div class="row">
    <div class="col-lg-8">
        <!-- Main Form -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-quote-left me-2"></i>
                    Testimonial Information
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data" id="testimonialForm">
                    <div class="row">
                        <!-- Client Name -->
                        <div class="col-md-6 mb-3">
                            <label for="client_name" class="form-label">
                                Client Name <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="client_name" name="client_name" 
                                   value="<?php echo htmlspecialchars($testimonial['client_name']); ?>" 
                                   required maxlength="255">
                            <div class="form-text">Full name of the client providing the testimonial</div>
                        </div>
                        
                        <!-- Client Position -->
                        <div class="col-md-6 mb-3">
                            <label for="client_position" class="form-label">Position/Title</label>
                            <input type="text" class="form-control" id="client_position" name="client_position" 
                                   value="<?php echo htmlspecialchars($testimonial['client_position']); ?>" 
                                   maxlength="255">
                            <div class="form-text">Job title or position (optional)</div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <!-- Client Company -->
                        <div class="col-md-6 mb-3">
                            <label for="client_company" class="form-label">Company/Organization</label>
                            <input type="text" class="form-control" id="client_company" name="client_company" 
                                   value="<?php echo htmlspecialchars($testimonial['client_company']); ?>" 
                                   maxlength="255">
                            <div class="form-text">Company or organization name (optional)</div>
                        </div>
                        
                        <!-- Rating -->
                        <div class="col-md-6 mb-3">
                            <label for="rating" class="form-label">
                                Rating <span class="text-danger">*</span>
                            </label>
                            <select class="form-select" id="rating" name="rating" required>
                                <option value="">Select Rating</option>
                                <?php for ($i = 5; $i >= 1; $i--): ?>
                                    <option value="<?php echo $i; ?>" <?php echo ($testimonial['rating'] == $i) ? 'selected' : ''; ?>>
                                        <?php echo $i; ?> Star<?php echo $i > 1 ? 's' : ''; ?>
                                        <?php echo str_repeat('★', $i) . str_repeat('☆', 5-$i); ?>
                                    </option>
                                <?php endfor; ?>
                            </select>
                        </div>
                    </div>
                    
                    <!-- Client Image -->
                    <div class="mb-3">
                        <label for="client_image" class="form-label">Client Photo</label>
                        
                        <?php if ($testimonial['client_image']): ?>
                            <div class="current-image mb-2">
                                <img src="../<?php echo htmlspecialchars($testimonial['client_image']); ?>" 
                                     alt="Current client photo" class="img-thumbnail" style="max-width: 100px; max-height: 100px;">
                                <div class="mt-1">
                                    <small class="text-muted">Current photo</small>
                                    <button type="button" class="btn btn-sm btn-outline-danger ms-2" onclick="removeCurrentImage()">
                                        <i class="fas fa-trash"></i> Remove
                                    </button>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <input type="file" class="form-control" id="client_image" name="client_image" 
                               accept="image/*">
                        <input type="hidden" id="remove_image" name="remove_image" value="0">
                        <div class="form-text">Upload a new photo to replace the current one (optional). Recommended size: 200x200px</div>
                        <div id="imagePreview" class="mt-2"></div>
                    </div>
                    
                    <!-- Testimonial Text -->
                    <div class="mb-3">
                        <label for="testimonial_text" class="form-label">
                            Testimonial Text <span class="text-danger">*</span>
                        </label>
                        <textarea class="form-control" id="testimonial_text" name="testimonial_text" 
                                  rows="6" required maxlength="1000"><?php echo htmlspecialchars($testimonial['testimonial_text']); ?></textarea>
                        <div class="form-text">
                            The testimonial content (max 1000 characters)
                            <span id="charCount" class="float-end">0/1000</span>
                        </div>
                    </div>
                    
                    <div class="row">
                        <!-- Status -->
                        <div class="col-md-4 mb-3">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-select" id="status" name="status">
                                <option value="active" <?php echo ($testimonial['status'] === 'active') ? 'selected' : ''; ?>>Active</option>
                                <option value="inactive" <?php echo ($testimonial['status'] === 'inactive') ? 'selected' : ''; ?>>Inactive</option>
                            </select>
                        </div>
                        
                        <!-- Display Order -->
                        <div class="col-md-4 mb-3">
                            <label for="display_order" class="form-label">Display Order</label>
                            <input type="number" class="form-control" id="display_order" name="display_order" 
                                   value="<?php echo $testimonial['display_order']; ?>" 
                                   min="1" max="999">
                            <div class="form-text">Order in which testimonial appears</div>
                        </div>
                        
                        <!-- Featured -->
                        <div class="col-md-4 mb-3">
                            <label class="form-label">Options</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="featured" name="featured" 
                                       value="1" <?php echo $testimonial['featured'] ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="featured">
                                    <i class="fas fa-star text-warning me-1"></i>
                                    Featured Testimonial
                                </label>
                                <div class="form-text">Show prominently on homepage</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Submit Buttons -->
                    <div class="d-flex gap-2 pt-3 border-top">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Update Testimonial
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="resetForm()">
                            <i class="fas fa-undo"></i> Reset Changes
                        </button>
                        <a href="testimonials.php" class="btn btn-outline-danger ms-auto">
                            <i class="fas fa-times"></i> Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- Preview Card -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-eye me-2"></i>
                    Live Preview
                </h6>
            </div>
            <div class="card-body">
                <div id="testimonialPreview" class="testimonial-preview-card">
                    <div class="rating-preview mb-2">
                        <span id="previewRating">
                            ★★★★★
                        </span>
                    </div>
                    <blockquote class="blockquote">
                        <p id="previewText" class="mb-3">"Enter testimonial text to see preview..."</p>
                    </blockquote>
                    <div class="d-flex align-items-center">
                        <div id="previewAvatar" class="avatar-placeholder me-3">
                            ?
                        </div>
                        <div>
                            <div id="previewName" class="fw-semibold">Client Name</div>
                            <small id="previewPosition" class="text-muted">Position at Company</small>
                        </div>
                    </div>
                    <div id="previewFeatured" class="mt-2" style="display: none;">
                        <span class="badge bg-warning">
                            <i class="fas fa-star"></i> Featured
                        </span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Info Card -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    Testimonial Details
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <div class="h5 mb-1"><?php echo $testimonial['rating']; ?>/5</div>
                            <small class="text-muted">Rating</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="h5 mb-1"><?php echo $testimonial['featured'] ? 'Yes' : 'No'; ?></div>
                        <small class="text-muted">Featured</small>
                    </div>
                </div>
                <hr>
                <div class="small">
                    <div class="mb-1">
                        <strong>Created:</strong> <?php echo date('M j, Y g:i A', strtotime($testimonial['created_at'])); ?>
                    </div>
                    <div class="mb-1">
                        <strong>Updated:</strong> <?php echo date('M j, Y g:i A', strtotime($testimonial['updated_at'])); ?>
                    </div>
                    <div>
                        <strong>Status:</strong> 
                        <span class="badge bg-<?php echo $testimonial['status'] == 'active' ? 'success' : 'secondary'; ?>">
                            <?php echo ucfirst($testimonial['status']); ?>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.testimonial-preview-card {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
    border-left: 4px solid #007bff;
}

.avatar-placeholder {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 1.2rem;
}

.rating-preview {
    font-size: 1.2rem;
    color: #ffc107;
}

#imagePreview img, .current-image img {
    max-width: 100px;
    max-height: 100px;
    border-radius: 8px;
    border: 2px solid #dee2e6;
}

.current-image.removed {
    opacity: 0.5;
    text-decoration: line-through;
}
</style>

<script>
let imageRemoved = false;

document.addEventListener('DOMContentLoaded', function() {
    // Character counter for testimonial text
    const testimonialText = document.getElementById('testimonial_text');
    const charCount = document.getElementById('charCount');

    function updateCharCount() {
        const count = testimonialText.value.length;
        charCount.textContent = count + '/1000';
        charCount.className = count > 900 ? 'float-end text-warning' : 'float-end';
        if (count > 1000) {
            charCount.className = 'float-end text-danger';
        }
    }

    testimonialText.addEventListener('input', updateCharCount);
    updateCharCount();

    // Image preview
    const imageInput = document.getElementById('client_image');
    const imagePreview = document.getElementById('imagePreview');

    imageInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                imagePreview.innerHTML = \`
                    <img src="\${e.target.result}" alt="Preview" class="img-thumbnail">
                    <div class="mt-1 text-muted small">\${file.name}</div>
                \`;
                updatePreview();
            };
            reader.readAsDataURL(file);
        } else {
            imagePreview.innerHTML = '';
            updatePreview();
        }
    });

    // Live preview updates
    function updatePreview() {
        const name = document.getElementById('client_name').value || 'Client Name';
        const position = document.getElementById('client_position').value;
        const company = document.getElementById('client_company').value;
        const text = document.getElementById('testimonial_text').value || 'Enter testimonial text to see preview...';
        const rating = parseInt(document.getElementById('rating').value) || 5;
        const featured = document.getElementById('featured').checked;

        // Update preview elements
        document.getElementById('previewName').textContent = name;

        let positionText = '';
        if (position && company) {
            positionText = position + ' at ' + company;
        } else if (position) {
            positionText = position;
        } else if (company) {
            positionText = company;
        } else {
            positionText = 'Position at Company';
        }
        document.getElementById('previewPosition').textContent = positionText;

        document.getElementById('previewText').textContent = '"' + text + '"';

        // Update rating stars
        const ratingStars = '★'.repeat(rating) + '☆'.repeat(5 - rating);
        document.getElementById('previewRating').textContent = ratingStars;

        // Update avatar
        const avatar = document.getElementById('previewAvatar');
        const newImagePreview = document.querySelector('#imagePreview img');
        const currentImage = document.querySelector('.current-image img');

        if (newImagePreview) {
            avatar.innerHTML = \`<img src="\${newImagePreview.src}" alt="\${name}" style="width: 50px; height: 50px; border-radius: 50%; object-fit: cover;">\`;
        } else if (currentImage && !imageRemoved) {
            avatar.innerHTML = \`<img src="\${currentImage.src}" alt="\${name}" style="width: 50px; height: 50px; border-radius: 50%; object-fit: cover;">\`;
        } else {
            avatar.textContent = name.charAt(0).toUpperCase();
        }

        // Update featured badge
        const featuredBadge = document.getElementById('previewFeatured');
        featuredBadge.style.display = featured ? 'block' : 'none';
    }

    // Add event listeners for live preview
    document.getElementById('client_name').addEventListener('input', updatePreview);
    document.getElementById('client_position').addEventListener('input', updatePreview);
    document.getElementById('client_company').addEventListener('input', updatePreview);
    document.getElementById('testimonial_text').addEventListener('input', updatePreview);
    document.getElementById('rating').addEventListener('change', updatePreview);
    document.getElementById('featured').addEventListener('change', updatePreview);

    // Initial preview update
    updatePreview();
});

function removeCurrentImage() {
    if (confirm('Are you sure you want to remove the current image?')) {
        imageRemoved = true;
        document.getElementById('remove_image').value = '1';
        document.querySelector('.current-image').classList.add('removed');
        document.querySelector('.current-image img').style.opacity = '0.3';

        // Update preview
        const avatar = document.getElementById('previewAvatar');
        const name = document.getElementById('client_name').value || 'Client Name';
        avatar.textContent = name.charAt(0).toUpperCase();
    }
}

function resetForm() {
    if (confirm('Are you sure you want to reset all changes? This will restore the original values.')) {
        location.reload();
    }
}

function deleteTestimonial() {
    if (confirm('Are you sure you want to delete this testimonial? This action cannot be undone.')) {
        fetch('testimonials.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                action: 'delete',
                id: <?php echo $testimonialId; ?>
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.location.href = 'testimonials.php';
            } else {
                alert('Failed to delete testimonial');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred');
        });
    }
}

// Form validation
document.getElementById('testimonialForm').addEventListener('submit', function(e) {
    const name = document.getElementById('client_name').value.trim();
    const text = document.getElementById('testimonial_text').value.trim();
    const rating = document.getElementById('rating').value;

    if (!name || !text || !rating) {
        e.preventDefault();
        alert('Please fill in all required fields (Client Name, Testimonial Text, and Rating).');
        return false;
    }

    if (text.length > 1000) {
        e.preventDefault();
        alert('Testimonial text must be 1000 characters or less.');
        return false;
    }

    return true;
});
</script>

<?php include 'includes/admin_footer.php'; ?>
