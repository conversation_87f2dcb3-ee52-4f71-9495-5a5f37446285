/**
 * Arcke Interior Design - Main JavaScript
 * Enhanced with modern ES6+ features, accessibility, and performance optimizations
 */

'use strict';

// ==========================================================================
// Configuration & Constants
// ==========================================================================

const CONFIG = {
    ANIMATION_DURATION: 300,
    SCROLL_THRESHOLD: 300,
    DEBOUNCE_DELAY: 100,
    INTERSECTION_THRESHOLD: 0.1,
    INTERSECTION_ROOT_MARGIN: '0px 0px -50px 0px'
};

// ==========================================================================
// Utility Functions
// ==========================================================================

/**
 * Debounce function to limit the rate of function execution
 * @param {Function} func - Function to debounce
 * @param {number} wait - Wait time in milliseconds
 * @returns {Function} Debounced function
 */
const debounce = (func, wait) => {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
};

/**
 * Check if user prefers reduced motion
 * @returns {boolean} True if user prefers reduced motion
 */
const prefersReducedMotion = () => {
    return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
};

/**
 * Safely query selector with error handling
 * @param {string} selector - CSS selector
 * @param {Element} context - Context element (default: document)
 * @returns {Element|null} Found element or null
 */
const safeQuerySelector = (selector, context = document) => {
    try {
        return context.querySelector(selector);
    } catch (error) {
        console.warn(`Invalid selector: ${selector}`, error);
        return null;
    }
};

/**
 * Safely query all selectors with error handling
 * @param {string} selector - CSS selector
 * @param {Element} context - Context element (default: document)
 * @returns {NodeList} Found elements
 */
const safeQuerySelectorAll = (selector, context = document) => {
    try {
        return context.querySelectorAll(selector);
    } catch (error) {
        console.warn(`Invalid selector: ${selector}`, error);
        return [];
    }
};

// ==========================================================================
// Main Application
// ==========================================================================

class ArckeApp {
    constructor() {
        this.isInitialized = false;
        this.observers = new Map();
        this.eventListeners = new Map();
    }

    /**
     * Initialize the application
     */
    init() {
        if (this.isInitialized) return;

        try {
            // Initialize all components
            this.initTestimonialSlider();
            this.initMobileNavigation();
            this.initSmoothScrolling();
            this.initBackToTop();
            this.initNewsletterForm();
            this.initProjectEffects();
            this.initServiceAnimations();
            this.initArticleEffects();
            this.initScrollIndicator();
            this.initHeaderScroll();
            this.initAccessibility();

            this.isInitialized = true;
            console.log('Arcke App initialized successfully');
        } catch (error) {
            console.error('Error initializing Arcke App:', error);
        }
    }

    /**
     * Clean up resources when needed
     */
    destroy() {
        // Clean up intersection observers
        this.observers.forEach(observer => observer.disconnect());
        this.observers.clear();

        // Remove event listeners
        this.eventListeners.forEach((listener, element) => {
            element.removeEventListener(listener.event, listener.handler);
        });
        this.eventListeners.clear();

        this.isInitialized = false;
    }

    /**
     * Initialize testimonial slider with Swiper
     */
    initTestimonialSlider() {
        if (typeof window.Swiper === 'undefined') {
            console.warn('Swiper library not loaded');
            return;
        }

        const sliderElement = safeQuerySelector('.testimonial-slider');
        if (!sliderElement) return;

        try {
            const swiper = new window.Swiper('.testimonial-slider', {
                loop: true,
                slidesPerView: 1,
                spaceBetween: 30,
                autoplay: {
                    delay: 5000,
                    disableOnInteraction: false,
                },
                navigation: {
                    nextEl: '.swiper-button-next',
                    prevEl: '.swiper-button-prev',
                },
                pagination: {
                    el: '.swiper-pagination',
                    clickable: true,
                },
                breakpoints: {
                    768: {
                        slidesPerView: 2,
                        spaceBetween: 30
                    },
                    1200: {
                        slidesPerView: 3,
                        spaceBetween: 30
                    }
                },
                a11y: {
                    enabled: true,
                    prevSlideMessage: 'Previous testimonial',
                    nextSlideMessage: 'Next testimonial',
                }
            });

            // Store reference for cleanup
            this.testimonialSwiper = swiper;
        } catch (error) {
            console.error('Error initializing testimonial slider:', error);
        }
    }

    /**
     * Initialize mobile navigation with accessibility features
     */
    initMobileNavigation() {
        const mobileToggle = safeQuerySelector('.mobile-nav-toggle');
        const mainNav = safeQuerySelector('.main-nav');

        if (!mobileToggle || !mainNav) return;

        // Create overlay element
        const overlay = document.createElement('div');
        overlay.className = 'mobile-nav-overlay';
        overlay.setAttribute('aria-hidden', 'true');
        document.body.appendChild(overlay);

        let isNavOpen = false;

        const toggleNav = () => {
            isNavOpen = !isNavOpen;

            mainNav.classList.toggle('active', isNavOpen);
            overlay.classList.toggle('active', isNavOpen);

            // Update ARIA attributes
            mobileToggle.setAttribute('aria-expanded', isNavOpen.toString());
            mainNav.setAttribute('aria-hidden', (!isNavOpen).toString());
            overlay.setAttribute('aria-hidden', (!isNavOpen).toString());

            // Toggle hamburger icon
            const icon = mobileToggle.querySelector('i');
            if (icon) {
                if (isNavOpen) {
                    icon.classList.remove('fa-bars');
                    icon.classList.add('fa-times');
                } else {
                    icon.classList.remove('fa-times');
                    icon.classList.add('fa-bars');
                }
            }

            // Manage focus
            if (isNavOpen) {
                const firstNavLink = mainNav.querySelector('a');
                if (firstNavLink) firstNavLink.focus();
            } else {
                mobileToggle.focus();
            }
        };

        const closeNav = () => {
            if (isNavOpen) toggleNav();
        };

        // Toggle button click
        mobileToggle.addEventListener('click', toggleNav);

        // Close on overlay click
        overlay.addEventListener('click', closeNav);

        // Close on nav link click (but not dropdown toggles)
        const navLinks = safeQuerySelectorAll('a:not(.dropdown-toggle)', mainNav);
        navLinks.forEach(link => {
            link.addEventListener('click', closeNav);
        });

        // Handle dropdown toggles in mobile
        const dropdownToggles = safeQuerySelectorAll('.dropdown-toggle', mainNav);
        dropdownToggles.forEach(toggle => {
            toggle.addEventListener('click', (e) => {
                e.preventDefault();
                const dropdown = toggle.parentElement;
                const dropdownMenu = dropdown.querySelector('.dropdown-menu');

                // Toggle dropdown
                dropdown.classList.toggle('active');

                // Update ARIA attributes
                const isExpanded = dropdown.classList.contains('active');
                toggle.setAttribute('aria-expanded', isExpanded.toString());

                // Animate dropdown height for mobile
                if (window.innerWidth <= 992) {
                    if (isExpanded) {
                        dropdownMenu.style.maxHeight = dropdownMenu.scrollHeight + 'px';
                    } else {
                        dropdownMenu.style.maxHeight = '0';
                    }
                }
            });
        });

        // Close on Escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && isNavOpen) {
                closeNav();
            }
        });

        // Store references for cleanup
        this.mobileNavElements = { mobileToggle, mainNav, overlay };
    }

    /**
     * Initialize smooth scrolling for anchor links
     */
    initSmoothScrolling() {
        const anchorLinks = safeQuerySelectorAll('a[href^="#"]');

        anchorLinks.forEach(link => {
            const handleClick = (e) => {
                const href = link.getAttribute('href');

                // Skip if it's just "#" or empty
                if (!href || href === '#') return;

                const targetElement = safeQuerySelector(href);

                if (targetElement) {
                    e.preventDefault();

                    const header = safeQuerySelector('.main-header');
                    const headerHeight = header ? header.offsetHeight : 0;
                    const targetPosition = targetElement.offsetTop - headerHeight;

                    // Use smooth scrolling if not reduced motion
                    const scrollBehavior = prefersReducedMotion() ? 'auto' : 'smooth';

                    window.scrollTo({
                        top: targetPosition,
                        behavior: scrollBehavior
                    });

                    // Update focus for accessibility
                    targetElement.focus({ preventScroll: true });
                }
            };

            link.addEventListener('click', handleClick);

            // Store for cleanup
            this.eventListeners.set(link, { event: 'click', handler: handleClick });
        });
    }

    /**
     * Initialize back to top button functionality
     */
    initBackToTop() {
        const backToTopBtn = safeQuerySelector('.back-to-top');
        if (!backToTopBtn) return;

        // Initially hide the button
        backToTopBtn.style.opacity = '0';
        backToTopBtn.style.visibility = 'hidden';
        backToTopBtn.style.transition = 'opacity 0.3s ease, visibility 0.3s ease';

        const handleScroll = debounce(() => {
            const shouldShow = window.pageYOffset > CONFIG.SCROLL_THRESHOLD;
            backToTopBtn.style.opacity = shouldShow ? '1' : '0';
            backToTopBtn.style.visibility = shouldShow ? 'visible' : 'hidden';
        }, CONFIG.DEBOUNCE_DELAY);

        const handleClick = (e) => {
            e.preventDefault();
            const scrollBehavior = prefersReducedMotion() ? 'auto' : 'smooth';
            window.scrollTo({
                top: 0,
                behavior: scrollBehavior
            });
        };

        window.addEventListener('scroll', handleScroll);
        backToTopBtn.addEventListener('click', handleClick);

        // Store for cleanup
        this.eventListeners.set(window, { event: 'scroll', handler: handleScroll });
        this.eventListeners.set(backToTopBtn, { event: 'click', handler: handleClick });
    }

    /**
     * Initialize newsletter form with validation
     */
    initNewsletterForm() {
        const newsletterForm = safeQuerySelector('.newsletter-form');
        if (!newsletterForm) return;

        const handleSubmit = (e) => {
            e.preventDefault();

            const emailInput = newsletterForm.querySelector('input[type="email"]');
            if (!emailInput) return;

            const email = emailInput.value.trim();

            if (email && this.isValidEmail(email)) {
                this.showNotification('Thank you for subscribing to our newsletter!', 'success');
                emailInput.value = '';
                emailInput.focus();
            } else {
                this.showNotification('Please enter a valid email address.', 'error');
                emailInput.focus();
            }
        };

        newsletterForm.addEventListener('submit', handleSubmit);
        this.eventListeners.set(newsletterForm, { event: 'submit', handler: handleSubmit });
    }

    /**
     * Validate email address
     * @param {string} email - Email to validate
     * @returns {boolean} True if valid
     */
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    /**
     * Show notification messages with accessibility
     * @param {string} message - Message to display
     * @param {string} type - Type of notification (success, error, info)
     */
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        notification.setAttribute('role', 'alert');
        notification.setAttribute('aria-live', 'polite');

        // Style the notification
        const backgroundColor = type === 'success' ? '#28a745' : '#dc3545';
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 5px;
            color: white;
            font-weight: 500;
            z-index: ${CONFIG.Z_MODAL || 1000};
            transform: translateX(100%);
            transition: transform ${CONFIG.ANIMATION_DURATION}ms ease;
            background-color: ${backgroundColor};
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        `;

        document.body.appendChild(notification);

        // Animate in
        requestAnimationFrame(() => {
            notification.style.transform = 'translateX(0)';
        });

        // Remove after 3 seconds
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, CONFIG.ANIMATION_DURATION);
        }, 3000);
    }

    /**
     * Initialize project overlay effects
     */
    initProjectEffects() {
        const projectItems = safeQuerySelectorAll('.project-item');

        projectItems.forEach(item => {
            const button = item.querySelector('.project-view-btn');

            if (button) {
                const handleClick = (e) => {
                    e.preventDefault();
                    // Add your lightbox or modal functionality here
                    console.log('Project clicked - implement lightbox/modal here');

                    // For now, just show a notification
                    this.showNotification('Project gallery coming soon!', 'info');
                };

                button.addEventListener('click', handleClick);
                this.eventListeners.set(button, { event: 'click', handler: handleClick });
            }
        });
    }

    /**
     * Initialize service card animations with Intersection Observer
     */
    initServiceAnimations() {
        const serviceCards = safeQuerySelectorAll('.service-card');
        if (serviceCards.length === 0) return;

        // Skip animations if user prefers reduced motion
        if (prefersReducedMotion()) {
            serviceCards.forEach(card => {
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            });
            return;
        }

        if ('IntersectionObserver' in window) {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, {
                threshold: CONFIG.INTERSECTION_THRESHOLD,
                rootMargin: CONFIG.INTERSECTION_ROOT_MARGIN
            });

            serviceCards.forEach(card => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                observer.observe(card);
            });

            this.observers.set('serviceCards', observer);
        }
    }

    /**
     * Initialize article card effects
     */
    initArticleEffects() {
        const articleCards = safeQuerySelectorAll('.article-card');

        articleCards.forEach(card => {
            const link = card.querySelector('a');
            if (link) {
                const handleClick = (e) => {
                    e.preventDefault();
                    // Add your article page navigation here
                    console.log('Article clicked - implement navigation here');
                    this.showNotification('Article page coming soon!', 'info');
                };

                link.addEventListener('click', handleClick);
                this.eventListeners.set(link, { event: 'click', handler: handleClick });
            }
        });
    }

    /**
     * Initialize scroll indicator functionality
     */
    initScrollIndicator() {
        const scrollIndicator = safeQuerySelector('.scroll-indicator');
        if (!scrollIndicator) return;

        const handleClick = () => {
            const aboutSection = safeQuerySelector('.about-section');
            if (aboutSection) {
                const header = safeQuerySelector('.main-header');
                const headerHeight = header ? header.offsetHeight : 0;
                const targetPosition = aboutSection.offsetTop - headerHeight;

                const scrollBehavior = prefersReducedMotion() ? 'auto' : 'smooth';
                window.scrollTo({
                    top: targetPosition,
                    behavior: scrollBehavior
                });
            }
        };

        scrollIndicator.addEventListener('click', handleClick);
        this.eventListeners.set(scrollIndicator, { event: 'click', handler: handleClick });
    }

    /**
     * Initialize header scroll effect
     */
    initHeaderScroll() {
        const header = safeQuerySelector('.main-header');
        if (!header) return;

        const handleScroll = debounce(() => {
            const shouldAddClass = window.pageYOffset > 50;
            header.classList.toggle('scrolled', shouldAddClass);
        }, CONFIG.DEBOUNCE_DELAY);

        window.addEventListener('scroll', handleScroll);
        this.eventListeners.set(window, { event: 'scroll', handler: handleScroll });
    }

    /**
     * Initialize accessibility features
     */
    initAccessibility() {
        // Add skip link if not present
        if (!safeQuerySelector('.skip-link')) {
            const skipLink = document.createElement('a');
            skipLink.href = '#main';
            skipLink.className = 'skip-link';
            skipLink.textContent = 'Skip to main content';
            document.body.insertBefore(skipLink, document.body.firstChild);
        }

        // Ensure main content has proper ID
        const main = safeQuerySelector('main');
        if (main && !main.id) {
            main.id = 'main';
        }
    }
}

// ==========================================================================
// Application Initialization
// ==========================================================================

// Initialize the application when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    const app = new ArckeApp();
    app.init();

    // Make app globally available for debugging
    window.ArckeApp = app;
});

// Handle page visibility changes
document.addEventListener('visibilitychange', () => {
    if (document.hidden) {
        // Page is hidden, pause any animations or timers
        console.log('Page hidden - pausing animations');
    } else {
        // Page is visible, resume animations
        console.log('Page visible - resuming animations');
    }
});
