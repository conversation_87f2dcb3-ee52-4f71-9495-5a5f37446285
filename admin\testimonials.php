<?php
require_once '../includes/functions.php';

// Check if user is logged in
requireLogin();

// Set page variables
$pageTitle = 'Testimonials Management';
$current_page = 'testimonials.php';

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    switch ($_POST['action']) {
        case 'toggle_status':
            $id = (int)$_POST['id'];
            $result = toggleTestimonialStatus($id);
            echo json_encode(['success' => $result]);
            exit;
            
        case 'delete':
            $id = (int)$_POST['id'];
            $result = deleteTestimonial($id);
            echo json_encode(['success' => $result]);
            exit;
            
        case 'update_order':
            $orders = $_POST['orders'];
            $success = true;
            foreach ($orders as $id => $order) {
                $testimonial = getTestimonialById($id);
                if ($testimonial) {
                    $testimonial['display_order'] = (int)$order;
                    if (!updateTestimonial($id, $testimonial)) {
                        $success = false;
                    }
                }
            }
            echo json_encode(['success' => $success]);
            exit;
    }
}

// Get filter parameters
$status_filter = isset($_GET['status']) ? sanitizeInput($_GET['status']) : '';
$search = isset($_GET['search']) ? sanitizeInput($_GET['search']) : '';

// Get all testimonials
$allTestimonials = getTestimonials($status_filter ?: null);

// Apply search filter
if ($search) {
    $allTestimonials = array_filter($allTestimonials, function($testimonial) use ($search) {
        return stripos($testimonial['client_name'], $search) !== false ||
               stripos($testimonial['client_company'], $search) !== false ||
               stripos($testimonial['testimonial_text'], $search) !== false;
    });
}

include 'includes/admin_header.php';
?>

<!-- Testimonials Management Content -->
<div class="page-header d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="mb-1">Testimonials Management</h2>
        <p class="text-muted mb-0">Manage client testimonials and reviews for your website.</p>
    </div>
    <div class="d-flex gap-2">
        <button type="button" class="btn btn-outline-secondary" data-bs-toggle="modal" data-bs-target="#filterModal">
            <i class="fas fa-filter"></i> Filter
        </button>
        <a href="testimonial-add.php" class="btn btn-primary">
            <i class="fas fa-plus"></i> Add New Testimonial
        </a>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <?php
    $totalTestimonials = count(getTestimonials());
    $activeTestimonials = count(getTestimonials('active'));
    $featuredTestimonials = count(getTestimonials('active', null, true));
    $avgRating = 0;
    
    if (!empty($allTestimonials)) {
        $totalRating = array_sum(array_column($allTestimonials, 'rating'));
        $avgRating = round($totalRating / count($allTestimonials), 1);
    }
    ?>
    <div class="col-md-3">
        <div class="stat-card">
            <div class="stat-icon bg-primary">
                <i class="fas fa-quote-left"></i>
            </div>
            <div class="stat-content">
                <h3><?php echo $totalTestimonials; ?></h3>
                <p>Total Testimonials</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stat-card">
            <div class="stat-icon bg-success">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="stat-content">
                <h3><?php echo $activeTestimonials; ?></h3>
                <p>Active Testimonials</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stat-card">
            <div class="stat-icon bg-warning">
                <i class="fas fa-star"></i>
            </div>
            <div class="stat-content">
                <h3><?php echo $featuredTestimonials; ?></h3>
                <p>Featured</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stat-card">
            <div class="stat-icon bg-info">
                <i class="fas fa-chart-line"></i>
            </div>
            <div class="stat-content">
                <h3><?php echo $avgRating; ?> <small class="text-muted">/ 5</small></h3>
                <p>Average Rating</p>
            </div>
        </div>
    </div>
</div>

<!-- Search and Filter Bar -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <label for="search" class="form-label">Search</label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="<?php echo htmlspecialchars($search); ?>" 
                       placeholder="Search by client name, company, or content...">
            </div>
            <div class="col-md-3">
                <label for="status" class="form-label">Status</label>
                <select class="form-select" id="status" name="status">
                    <option value="">All Status</option>
                    <option value="active" <?php echo $status_filter === 'active' ? 'selected' : ''; ?>>Active</option>
                    <option value="inactive" <?php echo $status_filter === 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                </select>
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="fas fa-search"></i> Search
                </button>
                <a href="testimonials.php" class="btn btn-outline-secondary">
                    <i class="fas fa-times"></i> Clear
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Testimonials Table -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="fas fa-quote-left me-2"></i>
            Testimonials (<?php echo count($allTestimonials); ?>)
        </h5>
        <div class="card-actions">
            <button type="button" class="btn btn-sm btn-outline-primary" onclick="toggleSortMode()">
                <i class="fas fa-sort"></i> Reorder
            </button>
        </div>
    </div>
    
    <?php if (!empty($allTestimonials)): ?>
        <div class="table-responsive">
            <table class="table table-hover mb-0" id="testimonialsTable">
                <thead class="table-light">
                    <tr>
                        <th width="50">#</th>
                        <th>Client</th>
                        <th>Testimonial</th>
                        <th width="100">Rating</th>
                        <th width="100">Status</th>
                        <th width="80">Featured</th>
                        <th width="120">Date</th>
                        <th width="150">Actions</th>
                    </tr>
                </thead>
                <tbody id="sortableTestimonials">
                    <?php foreach ($allTestimonials as $index => $testimonial): ?>
                        <tr data-id="<?php echo $testimonial['id']; ?>" data-order="<?php echo $testimonial['display_order']; ?>">
                            <td>
                                <span class="drag-handle" style="display: none; cursor: move;">
                                    <i class="fas fa-grip-vertical text-muted"></i>
                                </span>
                                <span class="row-number"><?php echo $index + 1; ?></span>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <?php if ($testimonial['client_image']): ?>
                                        <img src="../<?php echo htmlspecialchars($testimonial['client_image']); ?>" 
                                             alt="<?php echo htmlspecialchars($testimonial['client_name']); ?>" 
                                             class="rounded-circle me-3" width="40" height="40">
                                    <?php else: ?>
                                        <div class="avatar-placeholder me-3">
                                            <?php echo strtoupper(substr($testimonial['client_name'], 0, 1)); ?>
                                        </div>
                                    <?php endif; ?>
                                    <div>
                                        <div class="fw-semibold"><?php echo htmlspecialchars($testimonial['client_name']); ?></div>
                                        <small class="text-muted">
                                            <?php 
                                            $position_company = [];
                                            if ($testimonial['client_position']) $position_company[] = $testimonial['client_position'];
                                            if ($testimonial['client_company']) $position_company[] = $testimonial['client_company'];
                                            echo htmlspecialchars(implode(' at ', $position_company));
                                            ?>
                                        </small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="testimonial-preview">
                                    <?php echo htmlspecialchars(substr($testimonial['testimonial_text'], 0, 100)); ?>
                                    <?php if (strlen($testimonial['testimonial_text']) > 100): ?>
                                        <span class="text-muted">...</span>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td>
                                <div class="rating-stars">
                                    <?php for ($i = 1; $i <= 5; $i++): ?>
                                        <i class="fas fa-star <?php echo $i <= $testimonial['rating'] ? 'text-warning' : 'text-muted'; ?>"></i>
                                    <?php endfor; ?>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-<?php echo $testimonial['status'] == 'active' ? 'success' : 'secondary'; ?>">
                                    <?php echo ucfirst($testimonial['status']); ?>
                                </span>
                            </td>
                            <td>
                                <?php if ($testimonial['featured']): ?>
                                    <i class="fas fa-star text-warning" title="Featured"></i>
                                <?php else: ?>
                                    <i class="far fa-star text-muted" title="Not Featured"></i>
                                <?php endif; ?>
                            </td>
                            <td>
                                <small class="text-muted">
                                    <?php echo date('M j, Y', strtotime($testimonial['created_at'])); ?>
                                </small>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="testimonial-edit.php?id=<?php echo $testimonial['id']; ?>"
                                       class="btn btn-sm btn-outline-primary border-0 bg-primary bg-opacity-10 text-primary"
                                       title="Edit Testimonial" data-bs-toggle="tooltip">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button class="btn btn-sm btn-outline-<?php echo $testimonial['status'] == 'active' ? 'warning' : 'success'; ?> border-0 bg-<?php echo $testimonial['status'] == 'active' ? 'warning' : 'success'; ?> bg-opacity-10 text-<?php echo $testimonial['status'] == 'active' ? 'warning' : 'success'; ?>"
                                            onclick="toggleStatus(<?php echo $testimonial['id']; ?>)"
                                            title="<?php echo $testimonial['status'] == 'active' ? 'Deactivate' : 'Activate'; ?> Testimonial" data-bs-toggle="tooltip">
                                        <i class="fas fa-toggle-<?php echo $testimonial['status'] == 'active' ? 'on' : 'off'; ?>"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger border-0 bg-danger bg-opacity-10 text-danger"
                                            onclick="deleteTestimonial(<?php echo $testimonial['id']; ?>)"
                                            title="Delete Testimonial" data-bs-toggle="tooltip">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php else: ?>
        <div class="text-center py-5">
            <div class="mb-4">
                <i class="fas fa-quote-left fa-3x text-muted"></i>
            </div>
            <h5 class="text-muted">No testimonials found</h5>
            <p class="text-muted mb-4">Start building trust by adding client testimonials and reviews.</p>
            <a href="testimonial-add.php" class="btn btn-primary">
                <i class="fas fa-plus"></i> Add First Testimonial
            </a>
        </div>
    <?php endif; ?>
</div>

<style>
.stat-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.stat-content h3 {
    margin: 0;
    font-size: 1.8rem;
    font-weight: 700;
    color: #2c3e50;
}

.stat-content p {
    margin: 0;
    color: #6c757d;
    font-size: 0.9rem;
}

.avatar-placeholder {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 1rem;
}

.testimonial-preview {
    max-width: 300px;
    line-height: 1.4;
}

.rating-stars {
    font-size: 0.9rem;
}

.drag-handle {
    margin-right: 0.5rem;
}

#sortableTestimonials.sortable-mode .drag-handle {
    display: inline-block !important;
}

#sortableTestimonials.sortable-mode .row-number {
    display: none;
}

.ui-sortable-helper {
    background: #f8f9fa;
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}
</style>

<script>
let sortMode = false;

function toggleSortMode() {
    sortMode = !sortMode;
    const tbody = document.getElementById('sortableTestimonials');
    const button = event.target.closest('button');

    if (sortMode) {
        tbody.classList.add('sortable-mode');
        button.innerHTML = '<i class="fas fa-save"></i> Save Order';
        button.classList.remove('btn-outline-primary');
        button.classList.add('btn-success');

        // Enable sortable
        if (typeof $ !== 'undefined' && $.fn.sortable) {
            $('#sortableTestimonials').sortable({
                handle: '.drag-handle',
                helper: 'clone',
                update: function(event, ui) {
                    // Auto-save order when changed
                    saveOrder();
                }
            });
        }
    } else {
        tbody.classList.remove('sortable-mode');
        button.innerHTML = '<i class="fas fa-sort"></i> Reorder';
        button.classList.remove('btn-success');
        button.classList.add('btn-outline-primary');

        // Disable sortable
        if (typeof $ !== 'undefined' && $.fn.sortable) {
            $('#sortableTestimonials').sortable('destroy');
        }
    }
}

function saveOrder() {
    const rows = document.querySelectorAll('#sortableTestimonials tr');
    const orders = {};

    rows.forEach((row, index) => {
        const id = row.dataset.id;
        if (id) {
            orders[id] = index + 1;
        }
    });

    fetch('testimonials.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
            action: 'update_order',
            orders: JSON.stringify(orders)
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Order updated successfully!', 'success');
        } else {
            showNotification('Failed to update order', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('An error occurred', 'error');
    });
}

function toggleStatus(id) {
    if (!confirm('Are you sure you want to change the status of this testimonial?')) {
        return;
    }

    fetch('testimonials.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
            action: 'toggle_status',
            id: id
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            showNotification('Failed to update status', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('An error occurred', 'error');
    });
}

function deleteTestimonial(id) {
    if (!confirm('Are you sure you want to delete this testimonial? This action cannot be undone.')) {
        return;
    }

    fetch('testimonials.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
            action: 'delete',
            id: id
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            showNotification('Failed to delete testimonial', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('An error occurred', 'error');
    });
}

function showNotification(message, type) {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    // Auto-remove after 3 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 3000);
}

// Initialize tooltips
document.addEventListener('DOMContentLoaded', function() {
    if (typeof bootstrap !== 'undefined') {
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }
});
</script>

<?php include 'includes/admin_footer.php'; ?>
